// Script to upload template files to KV store
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Template file to upload - only need one template array for both basic and pro plans
const templates = [
  {
    key: 'basic_template_array_1536x1024',
    file: './src/templates/basic_template_array_1536x1024.json'
  }
];

// Process each template
templates.forEach(template => {
  try {
    console.log(`Uploading ${template.file} to KV as ${template.key}...`);
    
    // Read the file content
    const filePath = path.resolve(__dirname, template.file);
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Create a temp file with the content (wrangler can read value from file)
    const tempFile = path.resolve(__dirname, `${template.key}.temp.json`);
    fs.writeFileSync(tempFile, content);
    
    // Use wrangler to upload to KV with namespace ID (new syntax in Wrangler 4.x)
    const namespaceId = "4c3636a744c04c91bd62bccaafce8658"; // From wrangler.toml BRANDBOOK_TEMPLATES id
    const command = `npx wrangler kv key put ${template.key} --namespace-id=${namespaceId} --path=${tempFile} --remote`;
    console.log(`Executing: ${command}`);
    const result = execSync(command, { encoding: 'utf8' });
    console.log(result);
    
    // Clean up temp file
    fs.unlinkSync(tempFile);
    console.log(`Upload complete for ${template.key}\n`);
  } catch (error) {
    console.error(`Error uploading ${template.key}:`, error);
  }
});

console.log('All templates uploaded!');
