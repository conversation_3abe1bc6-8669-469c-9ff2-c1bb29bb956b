# MyBrandbook Backend

This is the Cloudflare Workers backend for the MyBrandbook application, which handles:

1. Form submission and session management
2. DodoPayments webhook processing
3. OpenAI integration for generating brand book configurations
4. Image generation with DALL-E
5. PDF compilation
6. Storage in R2
7. Email delivery

## Architecture

The backend is built on Cloudflare Workers with:

- **KV Namespaces** for storing:
  - Session data (`BRANDBOOK_SESSIONS`)
  - Page templates (`BRANDBOOK_TEMPLATES`)
  - Page generation status (`PAGE_STATUS`)
- **R2 Bucket** for storing generated PDFs
- **External APIs**:
  - OpenAI for text and image generation
  - DodoPayments for payment processing
  - Resend for email delivery

## API Endpoints

- `POST /submit-form` - Accepts brand book form data and returns a session ID
- `POST /webhook` - Handles DodoPayments webhook events
## Webhook Security

The `/webhook` endpoint implements DodoPayments webhooks per the [Standard Webhooks](https://standardwebhooks.com/) spec:

- Requires headers:
  - `webhook-id`: unique event identifier
  - `webhook-timestamp`: UNIX timestamp when event was sent
  - `webhook-signature`: HMAC SHA256 signature of the payload
- Compute signature: HMAC SHA256 of the string
  ```
  webhook-id.webhook-timestamp.<raw body>
  ```
  using your `DODO_WEBHOOK_SECRET`.
- Reject requests with non-matching signatures (respond with 400).
- Return 2xx on valid events; only `payment.succeeded` is processed.

## Internal Endpoints

- `POST /generate-config` - Internal endpoint for generating page configurations
- `POST /generate-brandbook` - Internal endpoint for generating brand book images
- `GET /session/:sessionId` - Retrieves session status
- `GET /health` - Simple health check

## Setup Instructions

### Prerequisites

- Cloudflare account
- Wrangler CLI installed (`npm install -g wrangler`)
- API keys for OpenAI, DodoPayments, and Resend

### Installation

1. Install dependencies:
   ```
   npm install
   ```

2. Create KV namespaces:
   ```
   wrangler kv:namespace create BRANDBOOK_SESSIONS
   wrangler kv:namespace create BRANDBOOK_TEMPLATES
   wrangler kv:namespace create PAGE_STATUS
   ```

3. Create R2 bucket:
   ```
   wrangler r2 bucket create brandbooks
   ```

4. Update `wrangler.toml` with your account ID and the IDs of the created KV namespaces and R2 bucket.

5. Upload templates to KV:
   ```
   wrangler kv:key put --binding=BRANDBOOK_TEMPLATES "template-basic" "$(cat src/templates/basic-template.json)" --path=wrangler.toml
   wrangler kv:key put --binding=BRANDBOOK_TEMPLATES "template-pro" "$(cat src/templates/pro-template.json)" --path=wrangler.toml
   ```

6. Set up secrets:
   ```
   wrangler secret put OPENAI_API_KEY
   wrangler secret put DODO_WEBHOOK_SECRET
   wrangler secret put RESEND_API_KEY
   ```

### Development

Run locally:
```
npm run dev
```

### Deployment

Deploy to Cloudflare:
```
npm run deploy
```

## Flow

1. User submits form → `/submit-form` → Session ID stored in KV
2. User completes checkout → DodoPayments sends webhook → `/webhook`
3. Worker processes webhook:
   - Loads session data from KV
   - Generates page configs via OpenAI
   - Generates images via DALL-E
   - Compiles PDFs
   - Uploads to R2
   - Sends email with links
4. User receives email with PDF links
