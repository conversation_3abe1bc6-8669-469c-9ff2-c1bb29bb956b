name = "mybrandbook-backend"
account_id = "9d7cec1b5a32b2df8c6cdc1321ccd00b"
compatibility_date = "2025-05-05"
main = "src/index.ts"

kv_namespaces = [
  { binding = "PAGE_STATUS", id = "59ee51731a7b4c09835559afe1005666" },
  { binding = "BRANDBOOK_SESSIONS", id = "80ea31a3dba5470fb947713c4e311a78" },
  { binding = "BRANDBOOK_TEMPLATES", id = "4c3636a744c04c91bd62bccaafce8658" }
]

r2_buckets = [
  { binding = "BRANDBOOKS", bucket_name = "brandbooks" }
]

[[queues.producers]]
queue = "brandbook-jobs"
binding = "BRANDBOOK_JOBS"

[[queues.consumers]]
queue = "brandbook-jobs"

[vars]
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
DODO_WEBHOOK_SECRET = "whsec_LDscPW97qCpySxt4NnLE6E4b"
RESEND_API_KEY = "re_MtNZkdRn_Hh7JGp9p6yD7XQUfbcabwbD3"
R2_PUBLIC_URL = "https://9d7cec1b5a32b2df8c6cdc1321ccd00b.r2.cloudflarestorage.com/brandbooks"

[observability.logs]
enabled = true