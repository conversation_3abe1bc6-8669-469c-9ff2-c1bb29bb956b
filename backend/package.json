{"name": "mybrandbook-backend", "version": "0.1.0", "private": true, "scripts": {"dev": "wrangler dev", "build": "npx wrangler@latest deploy src/index.ts --config wrangler.toml", "deploy": "NODE_OPTIONS=--no-experimental-fetch wrangler deploy", "setup-kv": "node src/setup-kv.js", "test": "vitest run"}, "dependencies": {"openai": "^4.97.0", "pdf-lib": "^1.17.1"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250506.0", "@cloudflare/workerd-darwin-arm64": "^1.20250506.0", "@types/node": "^20.17.42", "miniflare": "^3.20250408.1", "typescript": "^5.8.3", "undici": "^5.29.0", "vitest": "^0.34.6", "wrangler": "^4.14.2"}}