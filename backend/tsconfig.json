{"compilerOptions": {"target": "ES2020", "module": "ES2020", "lib": ["ES2020", "DOM"], "types": ["@cloudflare/workers-types"], "moduleResolution": "node", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "noImplicitThis": true, "alwaysStrict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}