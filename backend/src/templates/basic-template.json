[{"pageNumber": 1, "pageType": "cover", "placeholders": {"brandName": "{{brandName}}", "industry": "{{industry}}", "vibes": "{{vibes}}"}, "templateInstructions": "Create a stunning cover page that captures the essence of the brand. Use the brand name prominently and incorporate visual elements that reflect the industry and brand vibes."}, {"pageNumber": 2, "pageType": "brandIdentity", "placeholders": {"brandName": "{{brandName}}", "description": "{{description}}", "targetAudience": "{{targetAudience}}", "vibes": "{{vibes}}"}, "templateInstructions": "Design a brand identity page that showcases the brand's personality, values, and mission. Include visual elements that represent the target audience and brand description."}, {"pageNumber": 3, "pageType": "colorPalette", "placeholders": {"brandName": "{{brandName}}", "vibes": "{{vibes}}"}, "templateInstructions": "Create a color palette page that presents a harmonious set of colors that align with the brand's vibes and industry. Include primary and secondary colors with their hex codes."}]