{"brand_book_page_config": {"page_number": 2, "page_title": "Typography", "image_specs": {"style": "clean graphic design layout, minimalist brand guidelines page, typography focus, legible text", "resolution": "1536x1024", "aspect_ratio": "3:2", "orientation": "landscape", "file_format": "PNG"}, "layout_instructions": {"grid": "implied two-column layout, adjusted for 3:2 ratio, ensure comfortable reading space", "background_color": "[PLACEHOLDER_BACKGROUND_COLOR_HEX]", "text_style": "Use '[PLACEHOLDER_PRIMARY_FONT_NAME]' font for labels, color '[PLACEHOLDER_TEXT_COLOR_HEX]'. Ensure all text elements are clearly legible at 1536x1024."}, "page_elements": [{"element_id": "page_header", "type": "text_block", "position_description": "top-left corner, moderate margin", "size_description": "small but clear heading", "content_prompt": "Display the text 'Typography' styled as a small page header using [PLACEHOLDER_PRIMARY_FONT_NAME] font. Ensure readability."}, {"element_id": "primary_typeface_specimen", "type": "font_specimen", "position_description": "left column, occupying roughly top 60%", "size_description": "clear display, readable font sizes", "content_prompt": "Display Primary Typeface: Show '[PLACEHOLDER_PRIMARY_FONT_NAME]' name prominently. List available weights: [PLACEHOLDER_PRIMARY_FONT_WEIGHTS_LIST]. Show alphabet sample (Aa Bb Cc...) and numbers (0-9) in Regular weight at a readable size. Use color '[PLACEHOLDER_TEXT_COLOR_HEX]'. Label section 'Primary Typeface'."}, {"element_id": "secondary_typeface_specimen", "type": "font_specimen", "position_description": "left column, occupying roughly bottom 40%", "size_description": "medium display, readable font sizes", "content_prompt": "Display Secondary Typeface: Show '[PLACEHOLDER_SECONDARY_FONT_NAME]' name. List available weights: [PLACEHOLDER_SECONDARY_FONT_WEIGHTS_LIST]. Show alphabet sample (Aa Bb Cc...) in Regular weight at a readable size. Use color '[PLACEHOLDER_TEXT_COLOR_HEX]'. Label section 'Secondary Typeface'."}, {"element_id": "typographic_hierarchy_example", "type": "text_block_styled", "position_description": "right column, full height, clear vertical spacing", "size_description": "column width, ensuring text wrap is natural", "content_prompt": "Demonstrate typographic hierarchy based on rules '[PLACEHOLDER_TYPOGRAPHY_HIERARCHY_RULES]'. Show example text for 'Heading Level 1', 'Heading Level 2', and 'Body Paragraph...' styled correctly using specified fonts, weights, and appropriate sizes for readability on 1536x1024 canvas. Use color '[PLACEHOLDER_TEXT_COLOR_HEX]'. Ensure clear distinction between levels. Label section 'Hierarchy Example'."}]}}