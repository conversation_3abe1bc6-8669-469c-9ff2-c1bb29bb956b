import { OpenAI } from "openai";

// Types for the brand book page configuration (flattened)
interface BrandBookPageConfig {
  page_number: number;
  page_title: string;
  image_specs: {
    style: string;
    resolution: string;
    aspect_ratio: string;
    orientation: string;
    file_format: string;
  };
  layout_instructions: {
    grid: string;
    background_color: string;
    text_style: string;
  };
  page_elements: Array<{
    element_id: string;
    type: string;
    position_description: string;
    size_description: string;
    content_prompt: string;
  }>;
}

// Form input data structure
interface FormInput {
  brandName: string;
  industry: string;
  description: string;
  targetAudience: string;
  vibes: string[];
  email: string;
}

// Configuration for the OpenAI request
interface ConfigRequest {
  template: any[];
  formInput: FormInput;
  plan: "basic" | "pro";
}

// Response structure for the configs
interface ConfigResponse {
  configs: any[];
}

/**
 * Processes brand book page configurations using OpenAI
 * @param apiKey OpenAI API key
 * @param request Configuration request containing templates and form input
 * @returns Processed configurations for brand book pages
 */
export async function processBrandBookConfigs(
  apiKey: string,
  request: ConfigRequest
): Promise<ConfigResponse> {
  const openai = new OpenAI({ apiKey });

  // Construct the system prompt for the OpenAI request
  const systemPrompt = `You are Paula Scher, the legendary Pentagram partner and one of the world's foremost brand designers. You've defined iconic visual identities for Coca-Cola, Microsoft, and Citi. Your talent lies in translating brand essence into crystal-clear, artfully balanced design systems. Generate every BrandBookPageConfig as if you were crafting a premium, minimalist brand guideline—each page must be elegant, highly legible at 1536×1024, and perfectly organized.`;

  // Construct the user prompt for the OpenAI request
  const userPrompt = `Using the placeholder template array and formInput provided below, output an array of fully populated BrandBookPageConfig JSON objects.
- Respect the 3:2 landscape frame (1536×1024 PNG).
- Follow the schema exactly—do not add or remove fields.
- Infuse every prompt with the voice of a top-tier brand designer: precise, clear, and authoritative.
- Ensure text labels reference the user's font choices, hex values, and brand details.
- Maintain consistent style across pages: a clean grid layout, ample white space, and legible typography.
- For "basic" plan: generate exactly 3 pages. For "pro" plan: generate exactly 9 pages, grouping related topics (Logo, Typography, Color, Imagery, Iconography, Layout, Usage, Voice, Applications).

**Inputs**
\`\`\`json
${JSON.stringify(request, null, 2)}
\`\`\`

Output Requirements
- Return valid JSON only with this EXACT structure:

{
  "configs": [
    {
      "page_number": 1,
      "page_title": "Logo Guidelines",
      "image_specs": {
        "style": "clean graphic design layout",
        "resolution": "1536x1024",
        "aspect_ratio": "3:2",
        "orientation": "landscape",
        "file_format": "PNG"
      },
      "layout_instructions": {
        "grid": "implied clean grid system",
        "background_color": "#FFFFFF",
        "text_style": "Use 'Arial' font for labels"
      },
      "page_elements": [
        {
          "element_id": "page_header",
          "type": "text_block",
          "position_description": "top-left corner",
          "size_description": "small but clear heading",
          "content_prompt": "Display the text 'Logo Guidelines'"
        }
      ]
    }
  ]
}

- IMPORTANT: Do NOT nest the configs under 'brand_book_page_config' - each config should be a direct object in the configs array.
- Do not wrap the JSON in markdown or add commentary.
- Populate every [PLACEHOLDER_…] with the corresponding formInput values or resolved details.
- Craft each content_prompt as a clear directive to an image-generation model, e.g.:
"Display the primary NovaBloom logo—an abstract blooming flower with circuit-line details in #2A4A3A—centered at 60% width, on a #F5F5F5 canvas. Add a small 'Primary Logo' label in Satoshi Bold."
- Validate that each page's image_specs.resolution is "1536x1024" and file_format is "PNG".

Proceed to generate the configs array now.`;

  // Make the OpenAI request
  const response = await openai.chat.completions.create({
    model: "gpt-4.1-2025-04-14",
    messages: [
      { role: "system", content: systemPrompt },
      { role: "user", content: userPrompt }
    ],
    response_format: { type: "json_object" }
  });

  // Parse the response
  const content = response.choices[0].message.content;
  if (!content) {
    throw new Error("Failed to generate brand book configurations");
  }

  try {
    // Log the raw response for debugging
    console.log('Raw OpenAI response content:', content.substring(0, 200) + '...');

    const parsedResponse = JSON.parse(content) as ConfigResponse;

    // Validate the response
    if (!parsedResponse.configs || !Array.isArray(parsedResponse.configs)) {
      console.error('Invalid response structure:', JSON.stringify(parsedResponse, null, 2));
      throw new Error("Invalid response format: configs array is missing");
    }

    // Log the structure of the first config to help debug
    if (parsedResponse.configs.length > 0) {
      console.log('First config keys:', Object.keys(parsedResponse.configs[0]));
      console.log('First config preview:', JSON.stringify(parsedResponse.configs[0]).substring(0, 200) + '...');
    }

    // Normalize the configs - handle both direct and nested structures
    parsedResponse.configs = parsedResponse.configs.map((config, index) => {
      try {
        // Check if this is a nested structure with brand_book_page_config
        if (config.brand_book_page_config) {
          console.log(`Config ${index} has nested structure, flattening`);
          return config.brand_book_page_config;
        }

        // If it's already flat, just use it directly
        return config;
      } catch (error) {
        console.error(`Error normalizing config at index ${index}:`, error);
        throw new Error(`Failed to normalize config at index ${index}`);
      }
    });

    // Now validate and fix each config
    parsedResponse.configs.forEach((config, index) => {
      try {
        // Log the structure after normalization
        console.log(`Normalized config ${index} keys:`, Object.keys(config));

        const specs = config.image_specs;

        if (!specs) {
          console.error(`Missing image_specs in config ${index}:`, config);
          throw new Error(`Config at index ${index} is missing image_specs`);
        }

        if (specs.resolution !== "1536x1024") {
          specs.resolution = "1536x1024";
        }
        if (specs.file_format !== "PNG") {
          specs.file_format = "PNG";
        }
      } catch (configError) {
        console.error(`Error processing config at index ${index}:`, configError);
        throw configError; // Rethrow so we can catch it at the outer level
      }
    });

    return parsedResponse;
  } catch (error: any) {
    console.error("Failed to parse OpenAI response:", error);
    // Add more context to the error
    if (error instanceof SyntaxError) {
      console.error("JSON syntax error. First 500 chars of content:", content.substring(0, 500));
      throw new Error(`Failed to parse brand book configurations: JSON syntax error - ${error.message}`);
    } else {
      // Safely access error.message, defaulting to a string representation if not available
      const errorMessage = error.message || String(error);
      throw new Error(`Failed to parse brand book configurations: ${errorMessage}`);
    }
  }
}

/**
 * Generates image prompts for brand book pages
 * @param configs Brand book page configurations
 * @returns Array of prompts for image generation
 */
export function generateImagePrompts(configs: any[]): string[] {
  return configs.map((config, index) => {
    try {
      // Ensure we have the required fields
      if (!config.page_title || !config.image_specs || !config.layout_instructions || !config.page_elements) {
        console.error(`Config at index ${index} is missing required fields:`, config);
        throw new Error(`Config at index ${index} is missing required fields for image prompt generation`);
      }

      const { page_title, image_specs } = config;

      // Start with the style and specifications
      let prompt = `Create a professional brand book page titled "${page_title}".
Style: ${image_specs.style}.
Resolution: ${image_specs.resolution}.
Aspect ratio: ${image_specs.aspect_ratio}.
Orientation: ${image_specs.orientation}.
`;

      // Add layout instructions
      const { layout_instructions } = config;
      prompt += `Layout: ${layout_instructions.grid}.
Background color: ${layout_instructions.background_color}.
Text style: ${layout_instructions.text_style}.
`;

      // Add all page elements with their content prompts
      if (Array.isArray(config.page_elements)) {
        config.page_elements.forEach((element: {
          element_id: string;
          type: string;
          position_description: string;
          size_description: string;
          content_prompt: string;
        }) => {
          prompt += `\n${element.type} (${element.element_id}): ${element.content_prompt}
Position: ${element.position_description}.
Size: ${element.size_description}.
`;
        });
      } else {
        console.warn(`Config at index ${index} has no page_elements array, using simplified prompt`);
      }

      return prompt;
    } catch (error) {
      console.error(`Error generating prompt for config at index ${index}:`, error);
      // Return a fallback prompt that will still work with the image generation
      return `Create a professional brand book page for index ${index}.
Style: clean graphic design layout, minimalist brand guidelines page.
Resolution: 1536x1024.
Aspect ratio: 3:2.
Orientation: landscape.
Layout: balanced, well-spaced elements.
Background color: white.
Text style: Clean, modern sans-serif font in dark gray.
`;
    }
  });
}
