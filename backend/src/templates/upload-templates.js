// <PERSON><PERSON><PERSON> to upload the template files to the KV namespace
// Run this script with wrangler to upload the template files to the KV namespace
// Example: node upload-templates.js

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Read the template files
const page1Template = JSON.parse(fs.readFileSync(path.join(__dirname, 'page_1_logo_template_1536x1024.json'), 'utf8'));
const page2Template = JSON.parse(fs.readFileSync(path.join(__dirname, 'page_2_typography_template_1536x1024.json'), 'utf8'));
const page3Template = JSON.parse(fs.readFileSync(path.join(__dirname, 'page_3_color_graphics_template_1536x1024.json'), 'utf8'));

// Create a combined template array for basic plan
const basicTemplateArray = [page1Template, page2Template, page3Template];

// Write the combined template to a file
fs.writeFileSync(
  path.join(__dirname, 'basic_template_array_1536x1024.json'),
  JSON.stringify(basicTemplateArray, null, 2)
);

console.log('Basic template array written to basic_template_array_1536x1024.json');

// Upload the template files to KV
console.log('\nTo upload the templates to KV, run the following commands:');
console.log('\n# Upload individual page templates');
console.log(`wrangler kv:key put --binding=BRANDBOOK_TEMPLATES "page_1_logo_template_1536x1024" --path=./src/templates/page_1_logo_template_1536x1024.json`);
console.log(`wrangler kv:key put --binding=BRANDBOOK_TEMPLATES "page_2_typography_template_1536x1024" --path=./src/templates/page_2_typography_template_1536x1024.json`);
console.log(`wrangler kv:key put --binding=BRANDBOOK_TEMPLATES "page_3_color_graphics_template_1536x1024" --path=./src/templates/page_3_color_graphics_template_1536x1024.json`);

console.log('\n# Upload combined basic template array');
console.log(`wrangler kv:key put --binding=BRANDBOOK_TEMPLATES "basic_template_array_1536x1024" --path=./src/templates/basic_template_array_1536x1024.json`);

// Provide instructions for running the script
console.log('\nTo run these commands automatically, execute:');
console.log('cd /Users/<USER>/Desktop/Projects/Personal/MyBrandbookAI/mybrandbook/backend');
console.log('node src/templates/upload-templates.js | tail -n 5 | xargs -I{} sh -c "{}"');
