{"brand_book_page_config": {"page_number": 3, "page_title": "Color Palette & Graphics", "image_specs": {"style": "clean graphic design layout, minimalist brand guidelines page, color swatches, graphic elements display, clear labels", "resolution": "1536x1024", "aspect_ratio": "3:2", "orientation": "landscape", "file_format": "PNG"}, "layout_instructions": {"grid": "balanced layout, perhaps colors on top 2/3, graphics on bottom 1/3 or side-by-side if space allows", "background_color": "[PLACEHOLDER_BACKGROUND_COLOR_HEX]", "text_style": "Use '[PLACEHOLDER_PRIMARY_FONT_NAME]' font for labels and HEX codes, color '[PLACEHOLDER_TEXT_COLOR_HEX]'. Ensure text legibility."}, "page_elements": [{"element_id": "page_header", "type": "text_block", "position_description": "top-left corner, moderate margin", "size_description": "small but clear heading", "content_prompt": "Display the text 'Color & Graphics' styled as a small page header using [PLACEHOLDER_PRIMARY_FONT_NAME] font. Ensure readability."}, {"element_id": "color_palette_display", "type": "color_palette_display", "position_description": "top section or left two-thirds of page, organized grid/rows", "size_description": "clearly visible swatches, readable labels", "content_prompt": "Display the brand color palette clearly. Show Primary Colors: [PLACEHOLDER_PRIMARY_COLORS_HEX_NAMES_LIST]. Show Secondary Colors: [PLACEHOLDER_SECONDARY_COLORS_HEX_NAMES_LIST]. Show Accent Color: [PLACEHOLDER_ACCENT_COLOR_HEX_NAME]. Arrange in a grid or rows. Display HEX code and name below/beside each swatch using color '[PLACEHOLDER_TEXT_COLOR_HEX]'. Ensure labels are legible. Label sections 'Primary', 'Secondary', 'Accent'."}, {"element_id": "brand_pattern_swatch", "type": "graphic_element_swatch", "position_description": "bottom section or right third, top part", "size_description": "medium square swatch, pattern visible", "content_prompt": "Display a square swatch of the brand pattern: [PLACEHOLDER_PATTERN_DESCRIPTION]. Ensure pattern detail is visible at this resolution. Add label 'Brand Pattern'."}, {"element_id": "iconography_style_example", "type": "icon_display_grid", "position_description": "bottom section or right third, bottom part", "size_description": "row/grid of 2-3 small, clear icons", "content_prompt": "Display 2-3 sample icons demonstrating the iconography style: [PLACEHOLDER_ICONOGRAPHY_STYLE_DESCRIPTION]. Ensure icons are clear and distinct. Use color '[PLACEHOLDER_TEXT_COLOR_HEX]'. Add label 'Iconography Style'."}]}}