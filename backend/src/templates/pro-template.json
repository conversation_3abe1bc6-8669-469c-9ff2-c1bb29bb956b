[{"pageNumber": 1, "pageType": "cover", "placeholders": {"brandName": "{{brandName}}", "industry": "{{industry}}", "vibes": "{{vibes}}"}, "templateInstructions": "Create a stunning cover page that captures the essence of the brand. Use the brand name prominently and incorporate visual elements that reflect the industry and brand vibes."}, {"pageNumber": 2, "pageType": "brandStory", "placeholders": {"brandName": "{{brandName}}", "description": "{{description}}", "industry": "{{industry}}"}, "templateInstructions": "Design a brand story page that narrates the brand's journey, mission, and vision. Include visuals that support the narrative and reflect the brand's essence."}, {"pageNumber": 3, "pageType": "targetAudience", "placeholders": {"targetAudience": "{{targetAudience}}", "industry": "{{industry}}"}, "templateInstructions": "Create a target audience page that visually represents the ideal customers or users of the brand. Include demographics, psychographics, and user personas."}, {"pageNumber": 4, "pageType": "brandIdentity", "placeholders": {"brandName": "{{brandName}}", "description": "{{description}}", "vibes": "{{vibes}}"}, "templateInstructions": "Design a brand identity page that showcases the brand's personality, values, and unique selling propositions. Include visual elements that represent the brand's character."}, {"pageNumber": 5, "pageType": "colorPalette", "placeholders": {"brandName": "{{brandName}}", "vibes": "{{vibes}}"}, "templateInstructions": "Create a color palette page that presents a comprehensive set of colors that align with the brand's vibes and industry. Include primary, secondary, and accent colors with their hex codes."}, {"pageNumber": 6, "pageType": "typography", "placeholders": {"brandName": "{{brandName}}", "vibes": "{{vibes}}"}, "templateInstructions": "Design a typography page that presents font families and styles that complement the brand's personality. Include headings, body text, and special text treatments."}, {"pageNumber": 7, "pageType": "imageryStyle", "placeholders": {"brandName": "{{brandName}}", "industry": "{{industry}}", "vibes": "{{vibes}}"}, "templateInstructions": "Create an imagery style page that showcases the visual language of the brand through photography, illustration, or graphic styles. Include examples that reflect the brand's aesthetic."}, {"pageNumber": 8, "pageType": "brandApplication", "placeholders": {"brandName": "{{brandName}}", "industry": "{{industry}}"}, "templateInstructions": "Design a brand application page that demonstrates how the brand identity is applied across different mediums such as business cards, social media, website, or packaging."}, {"pageNumber": 9, "pageType": "brandGuidelines", "placeholders": {"brandName": "{{brandName}}", "vibes": "{{vibes}}"}, "templateInstructions": "Create a brand guidelines summary page that outlines the key rules for maintaining brand consistency. Include do's and don'ts for using the brand elements."}]