// Script to upload templates to KV namespace
const fs = require('fs');
const path = require('path');

// Load templates
const basicTemplate = require('./templates/basic-template.json');
const proTemplate = require('./templates/pro-template.json');

async function setupKV() {
  console.log('Setting up KV namespaces...');
  
  // This script would be run with wrangler to upload the templates
  // Example command: wrangler kv:key put --binding=BRANDBOOK_TEMPLATES "template-basic" "$(cat src/templates/basic-template.json)" --path=wrangler.toml
  
  console.log('Basic template:', JSON.stringify(basicTemplate, null, 2));
  console.log('Pro template:', JSON.stringify(proTemplate, null, 2));
  
  console.log('\nTo upload these templates to your KV namespace, run:');
  console.log('wrangler kv:namespace create BRANDBOOK_TEMPLATES');
  console.log('wrangler kv:namespace create BRANDBOOK_SESSIONS');
  console.log('\nThen update your wrangler.toml with the namespace IDs');
  console.log('\nFinally, upload the templates:');
  console.log('wrangler kv:key put --binding=BRANDBOOK_TEMPLATES "template-basic" "$(cat src/templates/basic-template.json)" --path=wrangler.toml');
  console.log('wrangler kv:key put --binding=BRANDBOOK_TEMPLATES "template-pro" "$(cat src/templates/pro-template.json)" --path=wrangler.toml');
}

setupKV().catch(console.error);
