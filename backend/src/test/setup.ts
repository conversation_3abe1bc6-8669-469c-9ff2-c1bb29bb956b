import { vi, afterAll } from 'vitest';

// Setup global mocks
vi.mock('openai', () => {
  return {
    OpenAI: vi.fn().mockImplementation(() => ({
      images: {
        generate: vi.fn().mockResolvedValue({
          data: [
            {
              b64_json: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg=="
            }
          ]
        })
      }
    }))
  };
});

// Mock fetch for external services
const originalFetch = global.fetch;
global.fetch = vi.fn().mockImplementation((url, options) => {
  // Mock Resend email API
  if (typeof url === 'string' && url.includes('resend.com')) {
    return Promise.resolve({
      ok: true,
      text: () => Promise.resolve(JSON.stringify({ id: "test_email_id", success: true })),
      json: () => Promise.resolve({ id: "test_email_id", success: true })
    });
  }
  
  // For other fetches, pass through to the original implementation or return a successful response
  return Promise.resolve({
    ok: true,
    arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
    text: () => Promise.resolve("{}"),
    json: () => Promise.resolve({})
  });
});

// Clean up after tests
afterAll(() => {
  vi.resetAllMocks();
  global.fetch = originalFetch;
});
