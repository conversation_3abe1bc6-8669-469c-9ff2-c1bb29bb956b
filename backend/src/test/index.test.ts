import { test, expect, describe, beforeAll, afterAll, vi } from 'vitest';
import { createServer } from 'http';
import { fetch } from 'undici';
import { Miniflare } from 'miniflare';

// Define types locally instead of importing from types.d.ts
interface ProcessPaymentWebhookPayload {
  type: string;
  data: {
    metadata?: {
      sessionId?: string;
    };
    [key: string]: any;
  };
}

interface SessionRecord {
  formData: any;
  timestamp: number;
  status: {
    pageConfigs: "pending" | "completed" | "error";
    pages: Record<number, "pending" | "completed" | "error">;
    pdfBundles: Record<string, string>;
    email: "pending" | "sent" | "error";
  };
  error?: string;
}

// Mock data for testing
const mockFormData = {
  brandName: "Thoughtseed",
  industry: "Technology",
  description: "Bespoke technology solutions for startups and enterprises",
  targetAudience: "Startups and enterprises",
  vibes: ["professional", "modern", "technical"],
  email: "mohan<PERSON>mm<PERSON><EMAIL>", // Using the user's email as specified
  plan: "basic" as const
};

const mockTemplateData = {
  template: {
    title: "Thoughtseed Brandbook",
    description: "A template for testing",
    placeholders: {
      "brand_name": "{{brandName}}",
      "industry": "{{industry}}",
      "description": "{{description}}",
      "target_audience": "{{targetAudience}}",
    },
    promptTemplate: "Create a brand image for {{brand_name}} in the {{industry}} industry. Description: {{description}}. Target audience: {{target_audience}}.",
    imageParams: {
      model: "gpt-image-1",
      size: "1536x1024",
      quality: "standard",
      style: "natural",
      output_format: "png", // Changed from response_format as it's not supported for gpt-image-1
      background: "transparent"
    }
  }
};

describe('Backend Integration Test', () => {
  let mf: Miniflare;
  let sessionId: string;

  beforeAll(async () => {
    // Create a Miniflare instance
    mf = new Miniflare({
      modules: true,
      scriptPath: "/Users/<USER>/Desktop/Projects/Personal/MyBrandbookAI/backend/src/index.ts",
      bindings: {
        OPENAI_API_KEY: 'test-api-key',
        DODO_WEBHOOK_SECRET: 'test-webhook-secret',
        RESEND_API_KEY: 'test-resend-key',
        PAGE_STATUS: {},
        BRANDBOOK_SESSIONS: {},
        BRANDBOOK_TEMPLATES: {},
        BRANDBOOKS: {}
      },
      kvNamespaces: ['PAGE_STATUS', 'BRANDBOOK_SESSIONS', 'BRANDBOOK_TEMPLATES'],
      r2Buckets: ['BRANDBOOKS']
    });

    // Prepare the mock data
    const kvStore = await mf.getKVNamespace('BRANDBOOK_TEMPLATES');
    await kvStore.put('brandbook-template', JSON.stringify(mockTemplateData));
    await kvStore.put('basic_template_array_1536x1024', JSON.stringify([mockTemplateData.template]));
  });

  afterAll(async () => {
    await mf.dispose();
  });

  test('Form submission endpoint creates a session', async () => {
    // Submit the form to create a session
    const formSubmissionResponse = await mf.dispatchFetch('http://localhost/submit-form', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(mockFormData)
    });

    expect(formSubmissionResponse.status).toBe(200);
    const data = await formSubmissionResponse.json() as { sessionId: string };
    sessionId = data.sessionId;
    expect(sessionId).toBeDefined();

    // Verify the session was stored in KV
    const sessions = await mf.getKVNamespace('BRANDBOOK_SESSIONS');
    const sessionData = await sessions.get(sessionId);
    expect(sessionData).toBeDefined();

    const parsedSession = JSON.parse(sessionData as string);
    expect(parsedSession.formData.brandName).toBe(mockFormData.brandName);
  });

  test('Manual webhook call to trigger brandbook generation', async () => {
    // Prepare webhook payload
    const mockPaymentData = {
      metadata: {
        sessionId
      }
    };

    // Create webhook payload
    const webhookPayload: ProcessPaymentWebhookPayload = {
      type: 'payment.succeeded',
      data: mockPaymentData
    };

    // Calculate mock signature for authentication bypass
    const secret = 'test-webhook-secret';
    const encoder = new TextEncoder();
    const webhookId = 'test-webhook-id';
    const timestamp = Date.now().toString();
    const payloadString = JSON.stringify(webhookPayload);

    const keyData = encoder.encode(secret);
    const cryptoKey = await crypto.subtle.importKey(
      "raw",
      keyData,
      { name: "HMAC", hash: "SHA-256" },
      false,
      ["sign"]
    );

    const data = encoder.encode(`${webhookId}.${timestamp}.${payloadString}`);
    const sigBuffer = await crypto.subtle.sign("HMAC", cryptoKey, data);
    const signature = Array.from(new Uint8Array(sigBuffer))
      .map(b => b.toString(16).padStart(2, "0"))
      .join("");

    // Call webhook endpoint
    const webhookResponse = await mf.dispatchFetch('http://localhost/webhook', {
      method: 'POST',
      headers: {
        'Content-Type': 'text/plain',
        'webhook-id': webhookId,
        'webhook-signature': signature,
        'webhook-timestamp': timestamp
      },
      body: payloadString
    });

    expect(webhookResponse.status).toBe(200);
    const responseData = await webhookResponse.json() as { received: boolean };
    expect(responseData.received).toBe(true);

    // Wait a bit for asynchronous processing
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check session status
    const sessionResponse = await mf.dispatchFetch(`http://localhost/session/${sessionId}`);
    expect(sessionResponse.status).toBe(200);

    const sessionStatus = await sessionResponse.json() as SessionRecord;
    console.log('Session status:', JSON.stringify(sessionStatus.status, null, 2));

    if (sessionStatus.status && sessionStatus.status.pdfBundles) {
      console.log('Generated PDF URLs:', Object.values(sessionStatus.status.pdfBundles));
    }

    console.log('Email status:', sessionStatus.status.email);
    console.log('Email recipient:', mockFormData.email);

    // Validate email was sent and PDFs were created
    expect(sessionStatus.status.email).toBe('sent');
    expect(Object.keys(sessionStatus.status.pdfBundles || {}).length).toBeGreaterThan(0);
  });
});
