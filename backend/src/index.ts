import { OpenAI } from "openai";
import { PDFDocument } from "pdf-lib";
import { processBrandBookConfigs, generateImagePrompts } from "./templates/brandbook-config-processor";

export interface Env {
  OPENAI_API_KEY: string;
  DODO_WEBHOOK_SECRET: string;
  RESEND_API_KEY: string;
  PAGE_STATUS: KVNamespace;
  BRANDBOOK_SESSIONS: KVNamespace;
  BRANDBOOK_TEMPLATES: KVNamespace;
  BRANDBOOKS: R2Bucket;
  R2_PUBLIC_URL: string;
  BRANDBOOK_JOBS: Queue; // Added for Cloudflare Queues
}

// Form submission data structure
interface BrandBookFormData {
  brandName: string;
  industry: string;
  description: string;
  targetAudience: string;
  vibes: string[];
  email: string;
  plan: "basic" | "pro";
}

// Session record structure
interface SessionRecord {
  formData: BrandBookFormData;
  timestamp: number;
  status: {
    pageConfigs: "pending" | "completed" | "error" | Record<string | number, boolean | "pending" | "completed" | "error">; // Modified for individual page status
    pages: Record<number, "pending" | "completed" | "error" | string>; // Modified to store image data/URL or status
    pdfBundles: Record<string, string>; // filename -> URL
    email: "pending" | "sent" | "error";
  };
  error?: string;
  jobIds?: string[]; // Added to store job IDs from queue
}

// Template structure
interface BrandBookTemplate {
  template: {
    title: string;
    description: string;
    placeholders: Record<string, string>;
    promptTemplate: string;
    imageParams: { // These are for the template definition
      model: string;
      size: string; // e.g. "1536x1024"
      quality: string; // e.g. "high"
      output_format: string; // e.g. "png"
      // response_format removed as it's not supported for gpt-image-1
      background: string; // e.g. "transparent"
    };
  };
}

// Page configuration for image generation
interface ImageParams {
  model: string;
  size: string;
  quality: string;
  output_format: string;
  // response_format removed as it's not supported for gpt-image-1
  background: string;
}

interface BrandBookPageConfig {
  pageNumber: number;
  prompt: string;
  imageParams: ImageParams;
}

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    const url = new URL(request.url);

    try {
      // Health check endpoint
      if (url.pathname === "/health" && request.method === "GET") {
        return new Response(JSON.stringify({ status: "ok" }), {
          headers: { "Content-Type": "application/json" },
        });
      }

      // Debug endpoint to check generation data
      if (url.pathname === "/debug" && url.searchParams.has("sessionId")) {
        const sessionId = url.searchParams.get("sessionId") as string;
        const debugData = await env.BRANDBOOK_SESSIONS.get(`debug_${sessionId}`);

        if (!debugData) {
          return new Response(JSON.stringify({ error: "Debug data not found" }), {
            status: 404,
            headers: { "Content-Type": "application/json" },
          });
        }

        return new Response(debugData, {
          headers: { "Content-Type": "application/json" },
        });
      }

      // PDF download endpoint
      if (url.pathname.startsWith("/download/") && request.method === "GET") {
        // Extract the file key from the URL
        const fileKey = url.pathname.substring("/download/".length);

        if (!fileKey) {
          return new Response("File key is required", { status: 400 });
        }

        try {
          // Get the file from R2
          const file = await env.BRANDBOOKS.get(fileKey);

          if (!file) {
            return new Response("File not found", { status: 404 });
          }

          // Return the file with appropriate headers
          return new Response(file.body, {
            headers: {
              "Content-Type": file.httpMetadata?.contentType || "application/pdf",
              "Content-Disposition": `attachment; filename="${fileKey.split('/').pop()}"`,
              "Cache-Control": "public, max-age=86400" // Cache for 24 hours
            }
          });
        } catch (error) {
          console.error(`Error retrieving file ${fileKey}:`, error);
          return new Response("Error retrieving file", { status: 500 });
        }
      }

      // Form submission endpoint
      if (url.pathname === "/submit-form" && request.method === "POST") {
        return handleFormSubmission(request, env);
      }

      // DodoPayments webhook endpoint
      if (url.pathname === "/webhook" && request.method === "POST") {
        return handleWebhook(request, env, ctx);
      }

      // Internal config generation endpoint
      if (url.pathname === "/generate-config" && request.method === "POST") {
        return handleConfigGeneration(request, env);
      }

      // Internal brandbook generation endpoint (now a producer)
      if (url.pathname === "/generate-brandbook" && request.method === "POST") {
        return handleBrandbookGenerationProducer(request, env); // Renamed to reflect producer role
      }

      // Session status endpoint
      if (url.pathname.startsWith("/session/") && request.method === "GET") {
        const sessionId = url.pathname.split("/session/")[1];
        return getSessionStatus(sessionId, env);
      }

      return new Response("Not Found", { status: 404 });
    } catch (error: any) {
      console.error("Unhandled error:", error);
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  },

  // Queue consumer for handling all async jobs (config generation and image generation)
  async queue(batch: MessageBatch, env: Env, ctx: ExecutionContext): Promise<void> {
    console.log(`Queue consumer started, processing ${batch.messages.length} messages`);

    for (const message of batch.messages) {
      try {
        // Parse message body as unknown first, then check job type
        const body = message.body as Record<string, unknown>;
        const jobType = (body.jobType as string) || 'image_generation';
        const sessionId = body.sessionId as string;

        console.log(`Processing job: ${message.id}, type: ${jobType}, sessionId: ${sessionId}`);
        console.log(`Job payload: ${JSON.stringify(body)}`);

        // Store job receipt in KV for debugging
        if (sessionId) {
          await env.BRANDBOOK_SESSIONS.put(`job_receipt_${message.id}`, JSON.stringify({
            timestamp: Date.now(),
            jobType,
            sessionId,
            messageId: message.id,
            body
          }));
        }

        // Check job type and route to appropriate handler
        if (jobType === 'config_generation') {
          // Handle config generation job
          const typedBody = body as {
            jobType: string;
            jobId: string;
            sessionId: string;
            templateKey: string;
          };

          await handleConfigGenerationJob(typedBody.jobId, typedBody.sessionId, typedBody.templateKey, env);
          console.log(`Successfully processed config generation job: ${typedBody.jobId}`);
        } else {
          // Default to image generation job (for backward compatibility)
          const typedBody = body as {
            jobId: string;
            sessionId: string;
            config: BrandBookPageConfig;
          };

          await handleImageGenerationJob(typedBody.jobId, typedBody.sessionId, typedBody.config, env);
          console.log(`Successfully processed image generation job: ${typedBody.jobId}`);
        }

        message.ack(); // Acknowledge message after successful processing
      } catch (error: any) {
        console.error(`Error processing job ${message.id}:`, error);

        // Extract sessionId for status update with proper type casting
        const body = message.body as Record<string, unknown>;
        const sessionId = body.sessionId as string;
        const jobType = body.jobType as string;

        if (sessionId) {
          if (jobType === 'config_generation') {
            // Update session status for config generation failure
            await updateSessionStatus(sessionId, env, {
              status: { pageConfigs: "error" },
              error: `Config generation failed: ${error.message || 'Unknown error'}`
            });
          } else if (body.config && typeof body.config === 'object') {
            const config = body.config as BrandBookPageConfig;
            if (config.pageNumber) {
              // Update session status for image generation failure
              await updateSessionStatus(sessionId, env, {
                status: { pages: { [config.pageNumber]: "error" } },
                error: `Image generation for page ${config.pageNumber} failed: ${error.message || 'Unknown error'}`
              });
            }
          }
        }

        message.ack(); // Acknowledge to prevent infinite retries for now, rely on job-level updates
      }
    }
  }
};

// Handle form submission and generate sessionId
async function handleFormSubmission(request: Request, env: Env): Promise<Response> {
  try {
    const formData: BrandBookFormData = await request.json();

    // Validate form data
    if (!formData.brandName || !formData.industry || !formData.description ||
        !formData.targetAudience || !formData.email || !formData.plan ||
        !Array.isArray(formData.vibes) || formData.vibes.length === 0) {
      return new Response(JSON.stringify({ error: "Missing required fields" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Generate a unique sessionId
    const sessionId = `sess_${crypto.randomUUID().replace(/-/g, "").substring(0, 12)}`;

    // Create session record
    const sessionRecord: SessionRecord = {
      formData,
      timestamp: Date.now(),
      status: {
        pageConfigs: "pending",
        pages: {},
        pdfBundles: {},
        email: "pending",
      },
    };

    // Store in KV
    await env.BRANDBOOK_SESSIONS.put(sessionId, JSON.stringify(sessionRecord));

    return new Response(JSON.stringify({ sessionId }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: any) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

// Handle DodoPayments webhook
async function handleWebhook(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
  const webhookId = request.headers.get("webhook-id") || "";
  const signature = request.headers.get("webhook-signature") || "";
  const timestamp = request.headers.get("webhook-timestamp") || "";
  const payload = await request.text();

  // Verify signature using Web Crypto
  const encoder = new TextEncoder();
  const keyData = encoder.encode(env.DODO_WEBHOOK_SECRET);
  const cryptoKey = await crypto.subtle.importKey(
    "raw",
    keyData,
    { name: "HMAC", hash: "SHA-256" },
    false,
    ["sign"]
  );
  const data = encoder.encode(`${webhookId}.${timestamp}.${payload}`);
  const sigBuffer = await crypto.subtle.sign("HMAC", cryptoKey, data);
  const expectedSignature = Array.from(new Uint8Array(sigBuffer))
    .map(b => b.toString(16).padStart(2, "0"))
    .join("");
  if (expectedSignature !== signature) {
    return new Response("Webhook Error: Invalid signature", { status: 400 });
  }

  let event: { type: string; data: any };
  try {
    event = JSON.parse(payload);
  } catch {
    return new Response("Webhook Error: Invalid payload", { status: 400 });
  }

  // Acknowledge receipt and process only payment.succeeded events
  if (event.type === "payment.succeeded") {
    ctx.waitUntil(processSession(event.data, env));
  }

  return new Response(JSON.stringify({ received: true }), {
    status: 200,
    headers: { "Content-Type": "application/json" },
  });
}

// Process DodoPayments payment data and queue a job for config generation
async function processSession(paymentData: any, env: Env) {
  try {
    // 1. Extract sessionId from metadata
    const sessionId = paymentData.metadata?.sessionId;
    if (!sessionId) {
      console.error("No sessionId in payment metadata");
      return;
    }

    // 2. Load session data from KV
    const sessionDataRaw = await env.BRANDBOOK_SESSIONS.get(sessionId);
    if (!sessionDataRaw) {
      console.error(`Session ${sessionId} not found`);
      return;
    }

    const sessionData: SessionRecord = JSON.parse(sessionDataRaw);

    // 3. Verify the template exists - use the correct key
    const templateKey = "basic_template_array_1536x1024";
    const templateExists = await env.BRANDBOOK_TEMPLATES.get(templateKey, { type: "stream" })
      .then(stream => stream !== null)
      .catch(() => false);

    if (!templateExists) {
      await updateSessionStatus(sessionId, env, {
        status: { pageConfigs: "error" },
        error: `Template not found: ${templateKey}`
      });
      return;
    }

    // 4. Instead of processing directly, queue a job for config generation
    // This avoids timeouts in the webhook handler
    const configJobId = `config_${crypto.randomUUID()}`;

    // Queue the config generation job
    await env.BRANDBOOK_JOBS.send({
      jobType: "config_generation",
      jobId: configJobId,
      sessionId,
      templateKey,
      timestamp: Date.now()
    });

    // Update session to show we've queued the config job
    await updateSessionStatus(sessionId, env, {
      status: { pageConfigs: "pending" },
      jobIds: [configJobId]
    });

    console.log(`Session ${sessionId} queued for config generation with job ${configJobId}`);

    // Note: The queue consumer will handle all async processing - config generation,
    // image generation, PDF creation, and email sending

  } catch (error: any) {
    console.error(`Error processing session: ${error.message || error}`);
    if (paymentData.metadata?.sessionId) {
      // Update session with error
      await updateSessionStatus(paymentData.metadata.sessionId, env, {
        status: { pageConfigs: "error" },
        error: `Processing error: ${error.message || error}`
      });
    }
  }
}

// Generate page configs using OpenAI
async function handleConfigGeneration(request: Request, env: Env): Promise<Response> {
  try {
    const { sessionId } = await request.json() as { sessionId: string };
    const session = await env.BRANDBOOK_SESSIONS.get(sessionId, { type: "json" }) as SessionRecord | null;

    if (!session) {
      return new Response(JSON.stringify({ error: "Session not found" }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Get the template array - same basic array for both plans, we'll process it differently
    const templateKey = "basic_template_array_1536x1024";
    const templateArrayString = await env.BRANDBOOK_TEMPLATES.get(templateKey);

    if (!templateArrayString) {
      return new Response(JSON.stringify({ error: `Template array not found: ${templateKey}` }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Parse the template array - should be an array of 3 page templates (logo, typography, color)
    const templateArray = JSON.parse(templateArrayString);

    // We'll need to process templates differently based on plan
    let intermediateConfigsResponse;

    if (session.formData.plan === "basic") {
      // Basic plan: Process the templates once for a single 3-page brandbook
      const configRequestPayload = {
        template: templateArray,
        formInput: session.formData,
        plan: session.formData.plan
      };

      intermediateConfigsResponse = await processBrandBookConfigs(env.OPENAI_API_KEY, configRequestPayload);
      console.log(`Basic plan: Generated ${intermediateConfigsResponse.configs.length} page configs`);
    } else {
      // Pro plan: Generate 3 completely different brandbooks
      // This requires 3 separate calls to brand config generation
      const brandPurposes = [
        "primary brand identity for standard corporate applications",
        "secondary brand identity for digital and product applications",
        "creative brand identity for marketing campaigns and special events"
      ];

      // Process each brandbook config separately - 3 distinctive brandbooks
      const allConfigs = [];

      for (let i = 0; i < 3; i++) {
        console.log(`Pro plan: Generating brandbook ${i+1}/3...`);

        // Make completely separate calls to generate each brandbook config
        // We're not just adding variation parameters, we're doing full separate generations
        const configRequestPayload = {
          template: templateArray,
          formInput: session.formData,
          plan: session.formData.plan
        };

        // Note: Each call to processBrandBookConfigs will generate a completely
        // different brandbook because OpenAI will produce different outputs even
        // with the same input due to temperature/sampling in the model
        const result = await processBrandBookConfigs(env.OPENAI_API_KEY, configRequestPayload);

        // Update page numbers to maintain sequence across the three brandbooks
        // First brandbook: pages 1-3, Second brandbook: pages 4-6, Third brandbook: pages 7-9
        result.configs.forEach((config, idx) => {
          config.brand_book_page_config.page_number = i * 3 + idx + 1;
          // Add a note about which brandbook this belongs to
          config.brand_book_page_config.brandbook_purpose = brandPurposes[i];
        });

        allConfigs.push(...result.configs);
      }

      intermediateConfigsResponse = { configs: allConfigs };
      console.log(`Pro plan: Total generated ${intermediateConfigsResponse.configs.length} page configs across 3 brandbooks`);
    }

    try {
      // Generate Image Gen prompts from these configs
      const imageGenPrompts = generateImagePrompts(intermediateConfigsResponse.configs);

      // Map to BrandBookPageConfig structure for our worker
      const finalPageConfigs: BrandBookPageConfig[] = imageGenPrompts.map((promptStr, index) => {
        // Get page number from the intermediate config
        const pageNumber = intermediateConfigsResponse.configs[index]?.brand_book_page_config?.page_number || index + 1;
        // Get page type from intermediate config for specific styling if needed
        const pageTitle = intermediateConfigsResponse.configs[index]?.brand_book_page_config?.page_title || "";

        const config: BrandBookPageConfig = {
          pageNumber: pageNumber,
          prompt: promptStr,
          imageParams: {
            model: "gpt-image-1",
            size: "1536x1024",
            quality: "high",
            output_format: "png", // Required by our interface but not used in API call
            background: "transparent"
          }
        };
        return config;
      });

      // Update session status to reflect that page configs are ready for generation
      await updateSessionStatus(sessionId, env, {
          status: {
              pageConfigs: finalPageConfigs.reduce((acc: Record<string | number, "pending" | "completed" | "error">, cfg: BrandBookPageConfig) => {
                  acc[cfg.pageNumber] = "pending";
                  return acc;
              }, {})
          }
      });

      return new Response(JSON.stringify({ configs: finalPageConfigs }), {
        headers: { "Content-Type": "application/json" },
      });
    } catch (error: any) {
      console.error("Error generating page configs:", error);
      return new Response(JSON.stringify({ error: error.message }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }
  } catch (error: any) {
    console.error("Config generation error:", error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

// Status checker for brandbook generation process
async function handleBrandbookGenerationProducer(request: Request, env: Env): Promise<Response> {
  try {
    const { sessionId } = await request.json() as { sessionId: string };

    if (!sessionId) {
      return new Response(JSON.stringify({ error: "Missing sessionId" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Retrieve session data to check status
    const session = await env.BRANDBOOK_SESSIONS.get(sessionId, { type: "json" }) as SessionRecord | null;
    if (!session) {
      return new Response(JSON.stringify({ error: "Session not found" }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Check session status to determine what to show the user
    if (session.error) {
      return new Response(JSON.stringify({
        success: false,
        status: session.status,
        error: session.error
      }), {
        status: 500,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Return the status of image generation and any PDF links
    return new Response(JSON.stringify({
      success: true,
      sessionId,
      status: session.status,
      jobIds: session.jobIds || [],
    }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: any) {
    console.error("Error in handleBrandbookGenerationProducer:", error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

// Consumer logic for config generation job
async function handleConfigGenerationJob(jobId: string, sessionId: string, templateKey: string, env: Env) {
  try {
    console.log(`Starting config generation job ${jobId} for session ${sessionId}`);

    // 1. Load session data
    const sessionData = await env.BRANDBOOK_SESSIONS.get(sessionId, { type: "json" }) as SessionRecord | null;
    if (!sessionData) {
      throw new Error(`Session ${sessionId} not found`);
    }

    // 2. Load template
    const templateRaw = await env.BRANDBOOK_TEMPLATES.get(templateKey);
    if (!templateRaw) {
      throw new Error(`Template ${templateKey} not found`);
    }

    // 3. Parse the template array
    const templateArray = JSON.parse(templateRaw);
    if (!Array.isArray(templateArray)) {
      throw new Error(`Template ${templateKey} is not an array`);
    }

    console.log(`Template loaded for job ${jobId}, array length: ${templateArray.length}`);

    // 4. Create config request
    const configRequest = {
      template: templateArray,
      formInput: sessionData.formData,
      plan: sessionData.formData.plan
    };

    // 5. Call OpenAI to generate configs
    console.log(`Calling OpenAI API for job ${jobId}`);
    const configResponse = await processBrandBookConfigs(env.OPENAI_API_KEY, configRequest);

    if (!configResponse || !configResponse.configs || !Array.isArray(configResponse.configs)) {
      throw new Error(`Failed to generate configs for job ${jobId}: invalid response`);
    }

    console.log(`OpenAI returned ${configResponse.configs.length} configs for job ${jobId}`);

    // 6. Update session status
    await updateSessionStatus(sessionId, env, {
      status: { pageConfigs: "completed" }
    });

    // 7. Generate image prompts from the configs
    console.log(`Generating image prompts from configs for job ${jobId}`);

    // Use the brand book config processor to generate prompts
    const imagePrompts = generateImagePrompts(configResponse.configs);

    if (!imagePrompts || imagePrompts.length !== configResponse.configs.length) {
      throw new Error(`Failed to generate image prompts for job ${jobId}`);
    }

    console.log(`Generated ${imagePrompts.length} image prompts for job ${jobId}`);

    // Store debug data in KV for later inspection
    const debugData = {
      configResponse: configResponse.configs,
      imagePrompts: imagePrompts,
      timestamp: Date.now()
    };

    // Store debug data in KV with sessionId as key
    await env.BRANDBOOK_SESSIONS.put(`debug_${sessionId}`, JSON.stringify(debugData));

    // Log detailed info about the first config and prompt
    if (imagePrompts.length > 0) {
      console.log(`Sample prompt for page 1: ${imagePrompts[0].substring(0, 200)}...`);
      console.log(`Sample config structure for page 1:`, JSON.stringify(configResponse.configs[0], null, 2));
    }

    // 8. Queue image generation jobs for each config with its prompt
    const imageJobIds: string[] = [];

    for (let i = 0; i < configResponse.configs.length; i++) {
      const richConfig = configResponse.configs[i]; // Renamed for clarity
      const imageJobId = `img_${crypto.randomUUID()}`;
      imageJobIds.push(imageJobId);

      // Construct the config payload for the image generation job queue
      // This must match the BrandBookPageConfig interface in index.ts
      const pageConfigForJob: BrandBookPageConfig = {
        pageNumber: richConfig.page_number || (i + 1), // Use page_number from rich config, fallback to index
        prompt: imagePrompts[i], // The specific DALL-E prompt for this page
        imageParams: {
          model: "gpt-image-1", // Default image generation model
          size: richConfig.image_specs?.resolution || "1536x1024", // Get size from rich config's image_specs
          quality: "high",                            // Default quality
          output_format: "png",                       // Required by our interface but not used in API call
          background: "transparent"                   // We want transparent background
        }
      };

      // Queue job for image generation
      await env.BRANDBOOK_JOBS.send({
        jobType: "image_generation",
        jobId: imageJobId,
        sessionId: sessionId,
        config: pageConfigForJob, // Send the correctly structured config
        requestUrl: env.R2_PUBLIC_URL // Pass the R2 URL for download links
      });
    }

    // 8. Store image job IDs
    await updateSessionStatus(sessionId, env, { jobIds: imageJobIds });
    console.log(`Config generation job ${jobId} completed successfully, queued ${imageJobIds.length} image jobs`);

  } catch (error: any) {
    console.error(`Error in config generation job ${jobId}:`, error);
    throw error; // Let the queue handler handle this error
  }
}

// Consumer logic for a single image generation job
async function handleImageGenerationJob(jobId: string, sessionId: string, config: BrandBookPageConfig, env: Env) {
  try {
    // Validate that the config is properly formed
    if (!config) {
      throw new Error(`Invalid config object for job ${jobId}`);
    }

    // Ensure pageNumber is set
    if (!config.pageNumber) {
      console.warn(`Missing pageNumber in config for job ${jobId}, using default value 1`);
      config.pageNumber = 1;
    }

    console.log(`Starting image generation for job ${jobId}, session ${sessionId}, page ${config.pageNumber}`);

    // Create OpenAI client
    const openai = new OpenAI({
      apiKey: env.OPENAI_API_KEY,
    });

    // Validate prompt
    if (!config.prompt) {
      throw new Error(`Missing prompt for image generation in page ${config.pageNumber}`);
    }

    // Extract imageParams from the config
    const {
      model = "gpt-image-1",
      size = "1536x1024",
      quality = "high"
      // output_format and background will be handled explicitly below
    } = config.imageParams || {}; // Ensure config.imageParams itself is not null/undefined

    // Log what we're about to do
    console.log(`Generating image with model ${model}, size ${size} for page ${config.pageNumber}`);

    // Prepare OpenAI API parameters with EXPLICIT fields
    const apiParams: OpenAI.ImageGenerateParams = {
      prompt: config.prompt,
      model: model, // Use destructured model
      n: 1,
      size: size as OpenAI.ImageGenerateParams['size'], // Use destructured size
      quality: quality as OpenAI.ImageGenerateParams['quality'], // Use destructured quality
      // response_format removed as it's not supported for gpt-image-1
      // output_format removed as it's not needed
      background: "transparent" // EXPLICITLY SET
    };

    // Store debug info in KV for troubleshooting
    const debugKey = `debug_${sessionId}_page_${config.pageNumber}`;
    // CORRECT KV STORE and log the explicitly constructed apiParams
    await env.BRANDBOOK_SESSIONS.put(debugKey, JSON.stringify({
      timestamp: Date.now(),
      apiParams: apiParams, // Log the apiParams we just constructed
      incomingImageParams: config.imageParams, // Log what came into this function
      apiKey: env.OPENAI_API_KEY ? "[PRESENT]" : "[MISSING]"
    }));

    // ... (rest of the code remains the same)
    let attempts = 0;
    const maxAttempts = 5; // increase retry attempts to handle rate limits
    let success = false;
    let imageData: string | undefined;

    while (attempts < maxAttempts && !success) {
      attempts++;
      try {
        console.log(`OpenAI attempt ${attempts}/${maxAttempts} for page ${config.pageNumber}`);
        console.log(`API params: ${JSON.stringify(apiParams)}`);

        // Log the API key length for debugging (never log the full key)
        console.log(`API key present: ${!!env.OPENAI_API_KEY}, length: ${env.OPENAI_API_KEY?.length || 0}`);

        const response = await openai.images.generate(apiParams);

        // Log successful response
        console.log(`OpenAI API response received for page ${config.pageNumber}:`,
                   JSON.stringify({
                     status: "success",
                     data_length: response.data?.length || 0,
                     has_b64: !!response.data?.[0]?.b64_json,
                     has_url: !!response.data?.[0]?.url
                   }));

        if (!response.data || response.data.length === 0) {
          throw new Error('No image data array received from OpenAI');
        }

        imageData = response.data[0].b64_json || response.data[0].url;

        if (!imageData) throw new Error('No image data (b64_json or url) received from OpenAI in the first element');

        // Store successful response in debug
        await env.BRANDBOOK_SESSIONS.put(`debug_${sessionId}_page_${config.pageNumber}_response`, JSON.stringify({
          timestamp: Date.now(),
          status: "success",
          has_data: true,
          data_type: response.data[0].b64_json ? "b64_json" : "url"
        }));

        success = true;
      } catch (e: any) {
        // Enhanced error logging
        console.error(`OpenAI API error (attempt ${attempts}/${maxAttempts}) for page ${config.pageNumber}:`, e);
        console.error(`Error details: ${JSON.stringify({
          message: e.message,
          status: e.status,
          code: e.code,
          type: e.type,
          param: e.param
        })}`);

        // Store error in debug data
        await env.BRANDBOOK_SESSIONS.put(`debug_${sessionId}_page_${config.pageNumber}_error_${attempts}`, JSON.stringify({
          timestamp: Date.now(),
          error: {
            message: e.message,
            status: e.status,
            code: e.code,
            type: e.type,
            param: e.param,
            stack: e.stack
          },
          apiParams
        }));

        if (attempts >= maxAttempts) {
          // Update session with detailed error
          await updateSessionStatus(sessionId, env, {
            status: { pages: { [config.pageNumber]: "error" } },
            error: `OpenAI API failed: ${e.message} (code: ${e.code || 'unknown'}, type: ${e.type || 'unknown'})`
          });

          throw new Error(`OpenAI API failed after ${maxAttempts} attempts for page ${config.pageNumber}: ${e.message}`);
        }

        const backoffMs = 5000 * Math.pow(2, attempts - 1); // 5s, 10s, 20s... exponential
        console.log(`Backing off for ${backoffMs}ms before retry ${attempts+1}`);
        await new Promise(resolve => setTimeout(resolve, backoffMs)); // Exponential backoff
      }
    }

    if (!imageData) {
      throw new Error(`Failed to generate image for page ${config.pageNumber} after ${maxAttempts} attempts.`);
    }

    const imageKey = `${sessionId}/images/page_${config.pageNumber}.png`; // Assuming b64_json -> png

    // Log the type of image data we received (without logging the actual data)
    console.log(`Image data type for page ${config.pageNumber}: ${typeof imageData === 'string' ?
      (imageData.startsWith('http') ? 'URL' :
       imageData.startsWith('data:') ? 'Data URL' :
       imageData.length > 100 ? 'Likely Base64' : 'Short string')
      : typeof imageData}`);

    let imageBuffer: ArrayBuffer;

    // Handle both URL and Base64 formats
    if (typeof imageData === 'string' && imageData.startsWith('http')) {
      // It's a URL, fetch the image
      console.log(`Fetching image from URL for page ${config.pageNumber}`);
      try {
        const imageFetchResponse = await fetch(imageData);
        if (!imageFetchResponse.ok) {
          const errorText = await imageFetchResponse.text();
          const errorMessage = `Failed to fetch image from URL for page ${config.pageNumber}: ${imageFetchResponse.status} ${imageFetchResponse.statusText}. Response: ${errorText}`;
          console.error(errorMessage);
          // Store this specific error to KV for debugging
          await env.BRANDBOOK_SESSIONS.put(`${sessionId}/debug_page_${config.pageNumber}_fetch_error`, JSON.stringify({
            timestamp: Date.now(),
            error: errorMessage,
            imageUrlStart: imageData.substring(0, 50) + '...'
          }));
          throw new Error(errorMessage);
        }
        imageBuffer = await imageFetchResponse.arrayBuffer();
      } catch (error) {
        const fetchError = error as Error;
        console.error(`Error fetching image URL for page ${config.pageNumber}:`, fetchError);
        throw new Error(`Failed to fetch image for page ${config.pageNumber}: ${fetchError.message || 'Unknown error'}`);
      }
    } else if (typeof imageData === 'string') {
      // It's Base64 data, convert it to an ArrayBuffer
      console.log(`Processing Base64 image data for page ${config.pageNumber}`);
      try {
        // Handle potential Data URL prefix
        const base64Data = imageData.includes(',') ? imageData.split(',')[1] : imageData;
        // Convert Base64 to binary
        const binaryString = atob(base64Data);
        // Create ArrayBuffer from binary string
        imageBuffer = new Uint8Array(binaryString.length).buffer;
        const bytes = new Uint8Array(imageBuffer);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
      } catch (error) {
        const base64Error = error as Error;
        console.error(`Error processing Base64 data for page ${config.pageNumber}:`, base64Error);
        throw new Error(`Failed to process Base64 image data for page ${config.pageNumber}: ${base64Error.message || 'Unknown error'}`);
      }
    } else {
      throw new Error(`Unsupported image data format for page ${config.pageNumber}`);
    }

    console.log(`Storing image ${imageKey} to R2, size: ${imageBuffer.byteLength} bytes`);
    await env.BRANDBOOKS.put(imageKey, imageBuffer, {
      httpMetadata: { contentType: 'image/png' },
    });

    const publicImageUrl = `${env.R2_PUBLIC_URL}/${imageKey}`;

    console.log(`Image for page ${config.pageNumber} stored at ${publicImageUrl}`);

    // Update session status with this image URL
    await updateSessionStatus(sessionId, env, {
      status: { pages: { [config.pageNumber]: publicImageUrl } } // Store public URL
    });

    console.log(`Job ${jobId} for page ${config.pageNumber} completed successfully.`);

    // After updating the status, check if all expected pages are complete
    // and trigger PDF generation if they are
    const sessionData = await env.BRANDBOOK_SESSIONS.get(sessionId, { type: "json" }) as SessionRecord | null;
    if (!sessionData) {
      console.error(`Session ${sessionId} not found when checking for PDF generation`);
      return;
    }

    // Get all page statuses
    const pageStatuses = sessionData.status?.pages || {};
    const expectedPageCount = sessionData.formData?.plan === "basic" ? 3 : 9; // Basic: 3 pages, Pro: 9 pages

    // Log all page statuses for debugging
    console.log(`All page statuses for session ${sessionId}:`, JSON.stringify(pageStatuses));

    const completedPages = Object.entries(pageStatuses).filter(([_, status]) =>
      typeof status === 'string' && status.startsWith('http')
    );

    console.log(`Completed pages: ${completedPages.length}/${expectedPageCount} for session ${sessionId}`);
    console.log(`Completed page numbers: ${completedPages.map(([pageNumber]) => pageNumber).join(', ')}`);

    // If all expected pages are complete, generate PDFs
    if (completedPages.length >= expectedPageCount) {
      console.log(`All ${expectedPageCount} pages complete for session ${sessionId}, generating PDFs...`);

      try {
        // Prepare image results in the expected format for createPDFs
        const imageResults = completedPages.map(([pageNumber, url]) => ({
          pageNumber: parseInt(pageNumber),
          image: url
        }));

        // Sort by page number
        imageResults.sort((a, b) => a.pageNumber - b.pageNumber);

        console.log(`Prepared ${imageResults.length} image results for PDF generation`);
        console.log(`Image result page numbers: ${imageResults.map(r => r.pageNumber).join(', ')}`);

        // Generate PDFs with the worker's URL for download links
        const workerUrl = "https://" + env.R2_PUBLIC_URL.split('/')[2]; // Extract domain from R2_PUBLIC_URL
        const pdfUrls = await createPDFs(sessionId, imageResults, sessionData.formData.plan, env, workerUrl);

        // Update session with PDF URLs
        await updateSessionStatus(sessionId, env, {
          status: { pdfBundles: pdfUrls }
        });

        console.log(`PDFs generated successfully for session ${sessionId}: ${JSON.stringify(pdfUrls)}`);
        console.log(`R2_PUBLIC_URL environment variable: ${env.R2_PUBLIC_URL}`);

        // Send email if an email address is provided
        if (sessionData.formData.email) {
          console.log(`Preparing to send email to ${sessionData.formData.email}`);

          try {
            await sendEmail(
              sessionData.formData.email,
              pdfUrls,
              sessionData.formData.brandName,
              env
            );

            // Update email status
            await updateSessionStatus(sessionId, env, {
              status: { email: "sent" }
            });

            console.log(`Email sent successfully to ${sessionData.formData.email}`);
          } catch (emailError: any) {
            console.error(`Failed to send email: ${emailError.message}`);
            console.error(`Email error details:`, emailError);

            await updateSessionStatus(sessionId, env, {
              status: { email: "error" },
              error: `Failed to send email: ${emailError.message}`
            });
          }
        } else {
          console.log(`No email address provided for session ${sessionId}, skipping email sending`);
        }
      } catch (pdfError: any) {
        console.error(`Failed to generate PDFs: ${pdfError.message}`);
        console.error(`PDF error details:`, pdfError);

        await updateSessionStatus(sessionId, env, {
          error: `Failed to generate PDFs: ${pdfError.message}`
        });
      }
    } else {
      console.log(`Not all pages are complete yet (${completedPages.length}/${expectedPageCount}), waiting for more pages to complete`);
    }

  } catch (error: any) {
    console.error(`Error in handleImageGenerationJob (job ${jobId}, page ${config.pageNumber}):`, error);
    await updateSessionStatus(sessionId, env, {
      status: { pages: { [config.pageNumber]: "error" } },
      error: `Failed to generate image for page ${config.pageNumber}: ${error.message}`
    });
    throw error; // Re-throw to let the queue handler know and potentially retry/DLQ
  }
}

// Get session status
async function getSessionStatus(sessionId: string, env: Env): Promise<Response> {
  try {
    const session = await env.BRANDBOOK_SESSIONS.get(sessionId, { type: "json" }) as SessionRecord | null;

    // Get any debug data for this session
    const debugData: Record<string, any> = {};
    const debugPrefix = `debug_${sessionId}`;

    // List all keys with the debug prefix
    const debugKeys = await env.BRANDBOOK_SESSIONS.list({ prefix: debugPrefix });

    // Fetch debug data if available
    for (const key of debugKeys.keys) {
      const data = await env.BRANDBOOK_SESSIONS.get(key.name, { type: "json" });
      if (data) {
        const shortKey = key.name.replace(debugPrefix, 'debug');
        debugData[shortKey] = data;
      }
    }
    if (!session) {
      return new Response(JSON.stringify({ error: "Session not found" }), {
        status: 404,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Create a completely new response object with only the fields we want
    // This ensures we don't include any raw image data
    const cleanedSession = {
      formData: session.formData,
      timestamp: session.timestamp,
      status: {
        pageConfigs: session.status.pageConfigs,
        // Transform the pages field to only include status strings
        pages: Object.entries(session.status.pages || {}).reduce((acc, [pageNumber, status]) => {
          // If the status is a URL (starts with http) or Base64 data, mark it as "completed"
          // Otherwise, keep the original status (pending, error, etc.)
          if (typeof status === 'string') {
            if (status.startsWith('http') || status.startsWith('data:') || status.length > 100) {
              acc[pageNumber] = "completed";
            } else {
              acc[pageNumber] = status;
            }
          } else {
            acc[pageNumber] = status;
          }
          return acc;
        }, {} as Record<string, string>),
        // Include email status
        email: session.status.email || "pending",
        // Include PDF URLs
        pdfBundles: session.status.pdfBundles || {}
      },
      error: session.error,
      jobIds: session.jobIds
    };

    // Include debug data in response if any exists
    const responseData = {
      ...cleanedSession,
      debug: Object.keys(debugData).length > 0 ? debugData : undefined
    };

    return new Response(JSON.stringify(responseData), {
      headers: { "Content-Type": "application/json" },
    });
  } catch (error: any) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

// Helper to update session status
async function updateSessionStatus(sessionId: string, env: Env, updates: { status?: Partial<SessionRecord['status']>, error?: string, jobIds?: string[] }) {
  try {
    let session = await env.BRANDBOOK_SESSIONS.get(sessionId, { type: "json" }) as SessionRecord | null;
    if (!session) {
      console.warn(`Session ${sessionId} not found for update. Updates: ${JSON.stringify(updates)}`);
      return;
    }

    const currentStatus = session.status || {}; // Ensure currentStatus is an object if session.status was undefined
    let newMergedStatus = { ...currentStatus }; // Start with current status

    if (updates.status) {
      // Merge top-level simple properties from updates.status
      if (updates.status.email !== undefined) newMergedStatus.email = updates.status.email;

      // Handle pageConfigs carefully
      if (updates.status.pageConfigs !== undefined) {
        if (typeof updates.status.pageConfigs === 'string') { // e.g., "pending", "completed", "error"
          newMergedStatus.pageConfigs = updates.status.pageConfigs;
        } else if (typeof updates.status.pageConfigs === 'object' && updates.status.pageConfigs !== null) { // It's a Record
          newMergedStatus.pageConfigs = {
            ...(typeof currentStatus.pageConfigs === 'object' && currentStatus.pageConfigs !== null ? currentStatus.pageConfigs : {}),
            ...updates.status.pageConfigs,
          };
        } // Potentially handle else case or assume valid input
      }

      // Handle pages (Record<number, string | ...>)
      if (updates.status.pages && typeof updates.status.pages === 'object' && updates.status.pages !== null) {
        newMergedStatus.pages = {
          ...(typeof currentStatus.pages === 'object' && currentStatus.pages !== null ? currentStatus.pages : {}),
          ...updates.status.pages,
        };
      }

      // Handle pdfBundles (Record<string, string>)
      if (updates.status.pdfBundles && typeof updates.status.pdfBundles === 'object' && updates.status.pdfBundles !== null) {
        newMergedStatus.pdfBundles = {
          ...(typeof currentStatus.pdfBundles === 'object' && currentStatus.pdfBundles !== null ? currentStatus.pdfBundles : {}),
          ...updates.status.pdfBundles,
        };
      }
    }
    session.status = newMergedStatus;

    if (updates.error) {
      session.error = session.error ? `${session.error}; ${updates.error}` : updates.error;
    }
    if (updates.jobIds) {
      session.jobIds = updates.jobIds;
    }

    await env.BRANDBOOK_SESSIONS.put(sessionId, JSON.stringify(session));
  } catch (e: any) {
    console.error(`Failed to update session ${sessionId}:`, e);
  }
}

// Create PDFs from images
async function createPDFs(sessionId: string, imageResults: any[], plan: string, env: Env, requestUrl?: string): Promise<Record<string, string>> {
  // Get the base URL for download links
  const baseUrl = requestUrl ? new URL(requestUrl).origin : env.R2_PUBLIC_URL.split('/brandbooks')[0];
  console.log(`Starting PDF creation for session ${sessionId} with plan ${plan}`);
  console.log(`Image results: ${JSON.stringify(imageResults.map(r => ({ pageNumber: r.pageNumber, hasImage: !!r.image, imageType: r.image && typeof r.image === 'string' ? (r.image.startsWith('http') ? 'URL' : 'Base64') : 'unknown' })))}`);
  console.log(`R2_PUBLIC_URL: ${env.R2_PUBLIC_URL}`);

  const pdfUrls: Record<string, string> = {};

  try {
    if (plan === "basic") {
      // Create a single PDF with all images
      console.log(`Creating basic PDF with ${imageResults.length} images`);
      const pdfDoc = await PDFDocument.create();

      for (const result of imageResults) {
        if (!result.image) {
          console.warn(`Skipping page ${result.pageNumber} - no image data`);
          continue;
        }

        try {
          // Handle both URL and base64 formats
          let imageBytes;
          if (result.image.startsWith('http')) {
            // Extract the image key from the URL
            // URL format: ${env.R2_PUBLIC_URL}/${imageKey}
            const imageUrl = result.image;
            console.log(`Processing R2 image URL for page ${result.pageNumber}: ${imageUrl.substring(0, 50)}...`);

            // Extract the key part from the URL
            const r2UrlPrefix = env.R2_PUBLIC_URL + '/';
            let imageKey;

            if (imageUrl.startsWith(r2UrlPrefix)) {
              imageKey = imageUrl.substring(r2UrlPrefix.length);
              console.log(`Extracted image key: ${imageKey}`);

              // Get the image directly from R2 instead of trying to fetch the URL
              try {
                const r2Object = await env.BRANDBOOKS.get(imageKey);
                if (!r2Object) {
                  throw new Error(`Image not found in R2: ${imageKey}`);
                }

                imageBytes = new Uint8Array(await r2Object.arrayBuffer());
                console.log(`Retrieved image from R2, size: ${imageBytes.byteLength} bytes`);
              } catch (error) {
                const r2Error = error as Error;
                console.error(`Error retrieving image from R2: ${r2Error}`);
                throw new Error(`Failed to retrieve image from R2: ${r2Error.message || 'Unknown error'}`);
              }
            } else {
              // If it's not an R2 URL, try to fetch it normally (unlikely to happen)
              console.log(`Fetching external image URL for page ${result.pageNumber}`);
              const response = await fetch(imageUrl);
              if (!response.ok) {
                throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
              }
              imageBytes = new Uint8Array(await response.arrayBuffer());
              console.log(`Fetched external image, size: ${imageBytes.byteLength} bytes`);
            }
          } else {
            console.log(`Converting Base64 image for page ${result.pageNumber}`);
            // Handle potential Base64 prefix
            const base64Data = result.image.includes(',') ? result.image.split(',')[1] : result.image;
            imageBytes = Uint8Array.from(atob(base64Data), c => c.charCodeAt(0));
            console.log(`Converted Base64 image, size: ${imageBytes.byteLength} bytes`);
          }

          console.log(`Embedding image for page ${result.pageNumber}`);
          const image = await pdfDoc.embedPng(imageBytes);

          console.log(`Adding page ${result.pageNumber} to PDF`);
          const page = pdfDoc.addPage([1536, 1024]);
          page.drawImage(image, {
            x: 0,
            y: 0,
            width: 1536,
            height: 1024,
          });
          console.log(`Added page ${result.pageNumber} to PDF`);
        } catch (pageError) {
          console.error(`Error processing page ${result.pageNumber}:`, pageError);
          // Continue with other pages
        }
      }

      console.log(`Saving basic PDF`);
      const pdfBytes = await pdfDoc.save();
      console.log(`PDF saved, size: ${pdfBytes.byteLength} bytes`);

      const pdfKey = `${sessionId}/basic_brandbook.pdf`;
      console.log(`Storing PDF at key: ${pdfKey}`);

      await env.BRANDBOOKS.put(pdfKey, pdfBytes, {
        httpMetadata: { contentType: 'application/pdf' },
      });
      console.log(`PDF stored in R2`);

      // Create a public download URL using our download endpoint
      const url = `${baseUrl}/download/${pdfKey}`;
      console.log(`PDF URL: ${url}`);

      pdfUrls["basic_brandbook.pdf"] = url;
    } else {
      // Pro plan: create 3 PDFs with 3 pages each
      console.log(`Creating pro PDFs with ${imageResults.length} images`);

      for (let i = 0; i < 3; i++) {
        console.log(`Creating pro PDF ${i+1}/3`);
        const pdfDoc = await PDFDocument.create();
        const startIdx = i * 3;
        const endIdx = Math.min(startIdx + 3, imageResults.length);
        console.log(`PDF ${i+1} will include pages from index ${startIdx} to ${endIdx-1}`);

        for (let j = startIdx; j < endIdx; j++) {
          const result = imageResults[j];
          if (!result.image) {
            console.warn(`Skipping page ${result.pageNumber} - no image data`);
            continue;
          }

          try {
            // Handle both URL and base64 formats
            let imageBytes;
            if (result.image.startsWith('http')) {
              // Extract the image key from the URL
              // URL format: ${env.R2_PUBLIC_URL}/${imageKey}
              const imageUrl = result.image;
              console.log(`Processing R2 image URL for page ${result.pageNumber}: ${imageUrl.substring(0, 50)}...`);

              // Extract the key part from the URL
              const r2UrlPrefix = env.R2_PUBLIC_URL + '/';
              let imageKey;

              if (imageUrl.startsWith(r2UrlPrefix)) {
                imageKey = imageUrl.substring(r2UrlPrefix.length);
                console.log(`Extracted image key: ${imageKey}`);

                // Get the image directly from R2 instead of trying to fetch the URL
                try {
                  const r2Object = await env.BRANDBOOKS.get(imageKey);
                  if (!r2Object) {
                    throw new Error(`Image not found in R2: ${imageKey}`);
                  }

                  imageBytes = new Uint8Array(await r2Object.arrayBuffer());
                  console.log(`Retrieved image from R2, size: ${imageBytes.byteLength} bytes`);
                } catch (error) {
                  const r2Error = error as Error;
                  console.error(`Error retrieving image from R2: ${r2Error}`);
                  throw new Error(`Failed to retrieve image from R2: ${r2Error.message || 'Unknown error'}`);
                }
              } else {
                // If it's not an R2 URL, try to fetch it normally (unlikely to happen)
                console.log(`Fetching external image URL for page ${result.pageNumber}`);
                const response = await fetch(imageUrl);
                if (!response.ok) {
                  throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
                }
                imageBytes = new Uint8Array(await response.arrayBuffer());
                console.log(`Fetched external image, size: ${imageBytes.byteLength} bytes`);
              }
            } else {
              console.log(`Converting Base64 image for page ${result.pageNumber}`);
              // Handle potential Base64 prefix
              const base64Data = result.image.includes(',') ? result.image.split(',')[1] : result.image;
              imageBytes = Uint8Array.from(atob(base64Data), c => c.charCodeAt(0));
              console.log(`Converted Base64 image, size: ${imageBytes.byteLength} bytes`);
            }

            console.log(`Embedding image for page ${result.pageNumber}`);
            const image = await pdfDoc.embedPng(imageBytes);

            console.log(`Adding page ${result.pageNumber} to PDF ${i+1}`);
            const page = pdfDoc.addPage([1536, 1024]);
            page.drawImage(image, {
              x: 0,
              y: 0,
              width: 1536,
              height: 1024,
            });
            console.log(`Added page ${result.pageNumber} to PDF ${i+1}`);
          } catch (pageError) {
            console.error(`Error processing page ${result.pageNumber} for PDF ${i+1}:`, pageError);
            // Continue with other pages
          }
        }

        console.log(`Saving pro PDF ${i+1}`);
        const pdfBytes = await pdfDoc.save();
        console.log(`PDF ${i+1} saved, size: ${pdfBytes.byteLength} bytes`);

        const pdfKey = `${sessionId}/pro-${i+1}.pdf`;
        console.log(`Storing PDF ${i+1} at key: ${pdfKey}`);

        await env.BRANDBOOKS.put(pdfKey, pdfBytes, {
          httpMetadata: { contentType: 'application/pdf' },
        });
        console.log(`PDF ${i+1} stored in R2`);

        // Create a public download URL using our download endpoint
        const url = `${baseUrl}/download/${pdfKey}`;
        console.log(`PDF ${i+1} URL: ${url}`);

        pdfUrls[`pro-${i+1}.pdf`] = url;
      }
    }

    console.log(`PDF creation completed successfully for session ${sessionId}`);
    console.log(`PDF URLs: ${JSON.stringify(pdfUrls)}`);
    return pdfUrls;
  } catch (error) {
    console.error(`Error in createPDFs for session ${sessionId}:`, error);
    throw error;
  }
}

// Send email with PDF links
async function sendEmail(email: string, pdfUrls: Record<string, string>, brandName: string, env: Env): Promise<void> {
  console.log(`Preparing to send email to ${email} for brand ${brandName}`);
  console.log(`PDF URLs to include in email: ${JSON.stringify(pdfUrls)}`);

  // Validate inputs
  if (!email || !email.includes('@')) {
    throw new Error(`Invalid email address: ${email}`);
  }

  if (!pdfUrls || Object.keys(pdfUrls).length === 0) {
    throw new Error('No PDF URLs provided for email');
  }

  if (!env.RESEND_API_KEY) {
    throw new Error('RESEND_API_KEY environment variable is not set');
  }

  // Create email content
  const urlList = Object.entries(pdfUrls)
    .map(([filename, url]) => `<li><a href="${url}">${filename}</a></li>`)
    .join("");

  const emailHtml = `
    <html>
      <body>
        <h1>Your Brand Book for ${brandName} is Ready!</h1>
        <p>Thank you for using MyBrandbook. Your brand book PDFs are ready to download:</p>
        <ul>${urlList}</ul>
        <p>The links will be valid for 30 days.</p>
      </body>
    </html>
  `;

  const emailText = `
    Your Brand Book for ${brandName} is Ready!

    Thank you for using MyBrandbook. Your brand book PDFs are ready to download:
    ${Object.entries(pdfUrls).map(([filename, url]) => `- ${filename}: ${url}`).join("\n")}

    The links will be valid for 30 days.
  `;

  // Prepare email request
  const emailRequest = {
    from: "<EMAIL>",
    to: email,
    subject: `Your Brand Book for ${brandName} is Ready!`,
    html: emailHtml,
    text: emailText
  };

  console.log(`Sending email with Resend API to ${email}`);
  console.log(`Email subject: ${emailRequest.subject}`);

  try {
    // Send email
    const response = await fetch("https://api.resend.com/emails", {
      method: "POST",
      headers: {
        "Authorization": `Bearer ${env.RESEND_API_KEY}`,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(emailRequest)
    });

    // Check response
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Resend API error (${response.status}): ${errorText}`);
      throw new Error(`Failed to send email: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const responseData = await response.json();
    console.log(`Email sent successfully to ${email}, Resend response:`, responseData);
  } catch (error) {
    console.error(`Error sending email to ${email}:`, error);
    throw error;
  }
}
