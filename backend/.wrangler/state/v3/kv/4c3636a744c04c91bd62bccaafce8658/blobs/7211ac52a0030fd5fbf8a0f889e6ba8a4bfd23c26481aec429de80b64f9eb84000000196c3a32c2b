[{"brand_book_page_config": {"page_number": 1, "page_title": "Logo Guidelines", "image_specs": {"style": "clean graphic design layout, minimalist brand guidelines page, information design, optimized for clarity", "resolution": "1536x1024", "aspect_ratio": "3:2", "orientation": "landscape", "file_format": "PNG"}, "layout_instructions": {"grid": "implied clean grid system, well-spaced elements, balanced layout for 3:2 aspect ratio", "background_color": "[PLACEHOLDER_BACKGROUND_COLOR_HEX]", "text_style": "Use '[PLACEHOLDER_PRIMARY_FONT_NAME]' font for labels and short descriptions, color '[PLACEHOLDER_TEXT_COLOR_HEX]'. Ensure text is legible and appropriately sized for 1536x1024 resolution."}, "page_elements": [{"element_id": "page_header", "type": "text_block", "position_description": "top-left corner, moderate margin", "size_description": "small but clear heading", "content_prompt": "Display the text 'Logo Guidelines' styled as a small page header using [PLACEHOLDER_PRIMARY_FONT_NAME] font. Ensure readability."}, {"element_id": "primary_logo_display", "type": "logo_display", "position_description": "left side, top-middle section, prominent placement", "size_description": "large focus, well-scaled for the canvas", "content_prompt": "Generate and prominently display the primary brand logo described as: [PLACEHOLDER_LOGO_DESCRIPTION]. Ensure high fidelity, clarity, and appropriate scale for 1536x1024. Add a small label 'Primary Logo' underneath."}, {"element_id": "logo_variations_display", "type": "logo_display_multiple", "position_description": "left side, below primary logo", "size_description": "medium size, displayed clearly, potentially stacked if horizontal space is tight", "content_prompt": "Generate and display logo variations based on: [PLACEHOLDER_LOGO_VARIATIONS_DESCRIPTIONS]. Label each variation clearly (e.g., 'Horizontal', 'Icon Only'). Ensure they are distinct and well-spaced."}, {"element_id": "clear_space_diagram", "type": "diagram", "position_description": "right side, top section", "size_description": "medium size, clear visual", "content_prompt": "Illustrate the clear space rule: Show the primary logo surrounded by a subtle boundary line or markers indicating minimum required clear space. Ensure the diagram is easy to understand. Add label 'Clear Space'."}, {"element_id": "minimum_size_example", "type": "logo_display_annotated", "position_description": "right side, middle section, below clear space", "size_description": "small but legible size", "content_prompt": "Display the primary logo at its minimum legible size for digital (e.g., scaled appropriately relative to a 32px reference height) and print (e.g., scaled relative to 0.75in). Add clear dimension labels (e.g., '32px H', '0.75in W'). Add label 'Minimum Size'."}, {"element_id": "incorrect_usage_display", "type": "usage_example_grid", "position_description": "bottom edge, spanning across middle section", "size_description": "row of 2-3 small, clear examples", "content_prompt": "Create a row of 2 or 3 clear visual examples demonstrating incorrect logo usage based on: [PLACEHOLDER_INCORRECT_USAGE_EXAMPLES_LIST]. Use simple visuals with red 'X' marks. Ensure examples are distinct. Label section 'Incorrect Usage'."}]}}, {"brand_book_page_config": {"page_number": 2, "page_title": "Typography", "image_specs": {"style": "clean graphic design layout, minimalist brand guidelines page, typography focus, legible text", "resolution": "1536x1024", "aspect_ratio": "3:2", "orientation": "landscape", "file_format": "PNG"}, "layout_instructions": {"grid": "implied two-column layout, adjusted for 3:2 ratio, ensure comfortable reading space", "background_color": "[PLACEHOLDER_BACKGROUND_COLOR_HEX]", "text_style": "Use '[PLACEHOLDER_PRIMARY_FONT_NAME]' font for labels, color '[PLACEHOLDER_TEXT_COLOR_HEX]'. Ensure all text elements are clearly legible at 1536x1024."}, "page_elements": [{"element_id": "page_header", "type": "text_block", "position_description": "top-left corner, moderate margin", "size_description": "small but clear heading", "content_prompt": "Display the text 'Typography' styled as a small page header using [PLACEHOLDER_PRIMARY_FONT_NAME] font. Ensure readability."}, {"element_id": "primary_typeface_specimen", "type": "font_specimen", "position_description": "left column, occupying roughly top 60%", "size_description": "clear display, readable font sizes", "content_prompt": "Display Primary Typeface: Show '[PLACEHOLDER_PRIMARY_FONT_NAME]' name prominently. List available weights: [PLACEHOLDER_PRIMARY_FONT_WEIGHTS_LIST]. Show alphabet sample (Aa Bb Cc...) and numbers (0-9) in Regular weight at a readable size. Use color '[PLACEHOLDER_TEXT_COLOR_HEX]'. Label section 'Primary Typeface'."}, {"element_id": "secondary_typeface_specimen", "type": "font_specimen", "position_description": "left column, occupying roughly bottom 40%", "size_description": "medium display, readable font sizes", "content_prompt": "Display Secondary Typeface: Show '[PLACEHOLDER_SECONDARY_FONT_NAME]' name. List available weights: [PLACEHOLDER_SECONDARY_FONT_WEIGHTS_LIST]. Show alphabet sample (Aa Bb Cc...) in Regular weight at a readable size. Use color '[PLACEHOLDER_TEXT_COLOR_HEX]'. Label section 'Secondary Typeface'."}, {"element_id": "typographic_hierarchy_example", "type": "text_block_styled", "position_description": "right column, full height, clear vertical spacing", "size_description": "column width, ensuring text wrap is natural", "content_prompt": "Demonstrate typographic hierarchy based on rules '[PLACEHOLDER_TYPOGRAPHY_HIERARCHY_RULES]'. Show example text for 'Heading Level 1', 'Heading Level 2', and 'Body Paragraph...' styled correctly using specified fonts, weights, and appropriate sizes for readability on 1536x1024 canvas. Use color '[PLACEHOLDER_TEXT_COLOR_HEX]'. Ensure clear distinction between levels. Label section 'Hierarchy Example'."}]}}, {"brand_book_page_config": {"page_number": 3, "page_title": "Color Palette & Graphics", "image_specs": {"style": "clean graphic design layout, minimalist brand guidelines page, color swatches, graphic elements display, clear labels", "resolution": "1536x1024", "aspect_ratio": "3:2", "orientation": "landscape", "file_format": "PNG"}, "layout_instructions": {"grid": "balanced layout, perhaps colors on top 2/3, graphics on bottom 1/3 or side-by-side if space allows", "background_color": "[PLACEHOLDER_BACKGROUND_COLOR_HEX]", "text_style": "Use '[PLACEHOLDER_PRIMARY_FONT_NAME]' font for labels and HEX codes, color '[PLACEHOLDER_TEXT_COLOR_HEX]'. Ensure text legibility."}, "page_elements": [{"element_id": "page_header", "type": "text_block", "position_description": "top-left corner, moderate margin", "size_description": "small but clear heading", "content_prompt": "Display the text 'Color & Graphics' styled as a small page header using [PLACEHOLDER_PRIMARY_FONT_NAME] font. Ensure readability."}, {"element_id": "color_palette_display", "type": "color_palette_display", "position_description": "top section or left two-thirds of page, organized grid/rows", "size_description": "clearly visible swatches, readable labels", "content_prompt": "Display the brand color palette clearly. Show Primary Colors: [PLACEHOLDER_PRIMARY_COLORS_HEX_NAMES_LIST]. Show Secondary Colors: [PLACEHOLDER_SECONDARY_COLORS_HEX_NAMES_LIST]. Show Accent Color: [PLACEHOLDER_ACCENT_COLOR_HEX_NAME]. Arrange in a grid or rows. Display HEX code and name below/beside each swatch using color '[PLACEHOLDER_TEXT_COLOR_HEX]'. Ensure labels are legible. Label sections 'Primary', 'Secondary', 'Accent'."}, {"element_id": "brand_pattern_swatch", "type": "graphic_element_swatch", "position_description": "bottom section or right third, top part", "size_description": "medium square swatch, pattern visible", "content_prompt": "Display a square swatch of the brand pattern: [PLACEHOLDER_PATTERN_DESCRIPTION]. Ensure pattern detail is visible at this resolution. Add label 'Brand Pattern'."}, {"element_id": "iconography_style_example", "type": "icon_display_grid", "position_description": "bottom section or right third, bottom part", "size_description": "row/grid of 2-3 small, clear icons", "content_prompt": "Display 2-3 sample icons demonstrating the iconography style: [PLACEHOLDER_ICONOGRAPHY_STYLE_DESCRIPTION]. Ensure icons are clear and distinct. Use color '[PLACEHOLDER_TEXT_COLOR_HEX]'. Add label 'Iconography Style'."}]}}]