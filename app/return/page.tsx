"use client"

import { useEffect, useState } from "react"
import { useSearchParams } from "next/navigation"
import { BrandBookProgress } from "@/components/brand-book-progress"
import { BrandBookResult } from "@/components/brand-book-result"
import { getSessionStatus, pollSessionStatus, SessionStatus } from "@/lib/api-client"

export default function ReturnPage() {
  const searchParams = useSearchParams()
  const [sessionId, setSessionId] = useState<string | null>(null)
  const [sessionStatus, setSessionStatus] = useState<SessionStatus | null>(null)
  const [pdfUrls, setPdfUrls] = useState<Record<string, string>>({})
  const [generationError, setGenerationError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const paymentId = searchParams.get('payment_id')
    const status = searchParams.get('status')
    
    if (status === 'succeeded' && paymentId) {
      // Payment was successful, we need to extract session ID from the payment
      // For now, we'll try to get it from localStorage if it was stored there
      const storedSessionId = localStorage.getItem('brandbook_session_id')
      
      if (storedSessionId) {
        setSessionId(storedSessionId)
        startPolling(storedSessionId)
      } else {
        setGenerationError('Session not found. Please try again.')
        setIsLoading(false)
      }
    } else if (status === 'failed') {
      setGenerationError('Payment failed. Please try again.')
      setIsLoading(false)
    } else {
      setGenerationError('Invalid payment status.')
      setIsLoading(false)
    }
  }, [searchParams])

  const startPolling = async (sessionId: string) => {
    try {
      setIsLoading(false)
      
      // Start polling for session status
      await pollSessionStatus(
        sessionId,
        (status) => {
          setSessionStatus(status)
          
          // If PDF is ready, update the PDF URLs
          if (Object.keys(status.status.pdfBundles).length > 0) {
            setPdfUrls(status.status.pdfBundles)
          }
          
          // Check for errors
          if (status.error) {
            setGenerationError(status.error)
          }
        },
        3000, // Poll every 3 seconds
        60    // Maximum 60 attempts (3 minutes)
      )
    } catch (error) {
      setGenerationError(error instanceof Error ? error.message : "An error occurred")
    }
  }

  const handleReset = () => {
    localStorage.removeItem('brandbook_session_id')
    window.location.href = '/'
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-12 w-12 border-4 border-purple-600 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-lg text-gray-600">Processing your payment...</p>
        </div>
      </div>
    )
  }

  if (generationError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-indigo-50 flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-8">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <h2 className="text-xl font-semibold text-red-800 mb-2">Error</h2>
            <p className="text-red-600 mb-4">{generationError}</p>
            <button
              onClick={() => window.location.href = '/'}
              className="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Go Back Home
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-indigo-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Your Brand Book is Being Generated
            </h1>
            <p className="text-gray-600">
              Thank you for your payment! We're creating your custom brand book now.
            </p>
          </div>

          {/* Show progress if session exists and PDF is not ready */}
          {sessionId && !Object.keys(pdfUrls).length && (
            <BrandBookProgress
              sessionId={sessionId}
              status={sessionStatus}
              email={sessionStatus?.formData?.email || ''}
              onComplete={(urls) => setPdfUrls(urls)}
            />
          )}
          
          {/* Show results if PDF is ready */}
          {Object.keys(pdfUrls).length > 0 && (
            <BrandBookResult
              pdfUrls={pdfUrls}
              onReset={handleReset}
            />
          )}
        </div>
      </div>
    </div>
  )
}
