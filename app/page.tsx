"use client"

import type React from "react"

import { useEffect, useRef, useState } from "react"
import { motion, useScroll, useTransform } from "framer-motion"
import { ChevronDown, Check, X, Download } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { ParticleBackground } from "@/components/particle-background"
import { IconCard } from "@/components/icon-card"
import { PricingCard } from "@/components/pricing-card"
import { PDFViewer } from "@/components/pdf-viewer"
import { FAQItem } from "@/components/faq-item"
import { useMobile } from "@/hooks/use-mobile"
import { BrandBookProgress } from "@/components/brand-book-progress"
import { BrandBookResult } from "@/components/brand-book-result"
import { submitBrandBookForm, getSessionStatus, pollSessionStatus, SessionStatus } from "@/lib/api-client"

export default function Home() {
  const isMobile = useMobile()
  const heroRef = useRef<HTMLDivElement>(null)
  const howItWorksRef = useRef<HTMLDivElement>(null)
  const examplesRef = useRef<HTMLDivElement>(null)
  const pricingRef = useRef<HTMLDivElement>(null)
  const formRef = useRef<HTMLFormElement>(null)
  const faqRef = useRef<HTMLDivElement>(null)

  const { scrollYProgress } = useScroll()
  const opacity = useTransform(scrollYProgress, [0, 0.2], [1, 0])
  const y = useTransform(scrollYProgress, [0, 0.2], [0, -50])

  const [formValues, setFormValues] = useState({
    brandName: "",
    industry: "",
    description: "",
    audience: "",
    vibes: [] as string[],
    email: "",
    pricing: "basic",
  })

  const [currentVibe, setCurrentVibe] = useState("")
  const [isFormValid, setIsFormValid] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeAccordion, setActiveAccordion] = useState<number | null>(null)

  // Brand book generation state
  const [sessionId, setSessionId] = useState<string | null>(null)
  const [sessionStatus, setSessionStatus] = useState<SessionStatus | null>(null)
  const [pdfUrls, setPdfUrls] = useState<Record<string, string>>({})
  const [generationError, setGenerationError] = useState<string | null>(null)

  // Validate form on input change
  useEffect(() => {
    validateForm()
  }, [formValues])

  const validateForm = () => {
    const { brandName, industry, description, audience, vibes, email } = formValues
    const isValid =
      brandName.trim() !== "" &&
      industry.trim() !== "" &&
      description.trim() !== "" &&
      audience.trim() !== "" &&
      vibes.length > 0 &&
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)

    setIsFormValid(isValid)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormValues({ ...formValues, [name]: value })
  }

  const handleVibeKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && currentVibe.trim() !== "" && formValues.vibes.length < 5) {
      e.preventDefault()
      setFormValues({
        ...formValues,
        vibes: [...formValues.vibes, currentVibe.trim()],
      })
      setCurrentVibe("")
    }
  }

  const removeVibe = (index: number) => {
    const newVibes = [...formValues.vibes]
    newVibes.splice(index, 1)
    setFormValues({ ...formValues, vibes: newVibes })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setGenerationError(null)

    try {
      // Format the form data for the API
      const formData = {
        brandName: formValues.brandName,
        industry: formValues.industry,
        description: formValues.description,
        targetAudience: formValues.audience,
        vibes: formValues.vibes,
        email: formValues.email,
        plan: formValues.pricing as "basic" | "pro"
      }

      // Submit the form to create a session
      const { sessionId } = await submitBrandBookForm(formData)
      setSessionId(sessionId)

      // Start polling for session status
      pollSessionStatus(
        sessionId,
        (status) => {
          setSessionStatus(status)
          
          // If PDF is ready, update the PDF URLs
          if (Object.keys(status.status.pdfBundles).length > 0) {
            setPdfUrls(status.status.pdfBundles)
          }
          
          // Check for errors
          if (status.error) {
            setGenerationError(status.error)
          }
        },
        3000, // Poll every 3 seconds
        60    // Maximum 60 attempts (3 minutes)
      ).catch((error) => {
        setGenerationError(error.message)
      })
    } catch (error) {
      console.error("Error submitting form:", error)
      setGenerationError(error instanceof Error ? error.message : "An error occurred")
      setIsSubmitting(false)
    }
  }

  // Reset the form and generation state
  const handleReset = () => {
    setSessionId(null)
    setSessionStatus(null)
    setPdfUrls({})
    setGenerationError(null)
    setIsSubmitting(false)
  }

  const scrollToSection = (ref: React.RefObject<HTMLDivElement>) => {
    ref.current?.scrollIntoView({ behavior: "smooth" })
  }

  const toggleAccordion = (index: number) => {
    setActiveAccordion(activeAccordion === index ? null : index)
  }

  const handleDownloadPDF = () => {
    // In a real app, this would download the actual PDF
    const link = document.createElement("a")
    link.href = "/assets/sample-brandbook.pdf"
    link.download = "sample-brandbook.pdf"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <main className="relative min-h-screen overflow-hidden">
      {/* Particle Background - Fixed across all sections */}
      <div className="fixed inset-0 z-0 pointer-events-none">
        <ParticleBackground />
      </div>

      {/* Hero Section */}
      <section
        ref={heroRef}
        id="hero"
        className="relative min-h-screen flex flex-col items-center justify-center px-4 overflow-hidden"
      >
        <motion.div
          className="relative z-10 text-center max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.h1
            className="text-5xl md:text-7xl lg:text-8xl font-black tracking-tight text-gray-900 mb-6"
            style={{ y, opacity }}
          >
            <motion.span
              className="inline-block"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
            >
              AI-Powered
            </motion.span>{" "}
            <motion.span
              className="inline-block bg-gradient-to-r from-purple-600 to-indigo-600 text-transparent bg-clip-text"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
            >
              Brand Book
            </motion.span>{" "}
            <motion.span
              className="inline-block"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.6 }}
            >
              Creator
            </motion.span>
          </motion.h1>

          <motion.p
            className="text-xl md:text-2xl text-gray-600 mb-10 max-w-2xl mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.8 }}
          >
            Define your brand identity in minutes with custom AI magic
          </motion.p>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 1, duration: 0.5 }}
          >
            <Button
              onClick={() => scrollToSection(formRef)}
              className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-medium py-6 px-8 rounded-full text-lg shadow-lg hover:shadow-xl transition-all duration-300"
            >
              Create Your Brand Book
            </Button>
          </motion.div>
        </motion.div>

        <motion.div
          className="absolute bottom-10 left-1/2 transform -translate-x-1/2 z-10"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.5, duration: 0.5 }}
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ repeat: Number.POSITIVE_INFINITY, duration: 1.5 }}
            onClick={() => scrollToSection(howItWorksRef)}
            className="cursor-pointer"
          >
            <ChevronDown className="h-8 w-8 text-gray-400" />
          </motion.div>
        </motion.div>
      </section>

      {/* How It Works Section */}
      <section ref={howItWorksRef} id="how-it-works" className="relative py-24 px-4 z-10">
        <div className="absolute inset-0 bg-white/70 backdrop-blur-sm z-0"></div>
        <div className="max-w-6xl mx-auto relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-4">How It Works</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Three simple steps to your professional brand book
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
            <IconCard
              icon="✏️"
              title="Enter Your Brand"
              description="Fill in a simple form with your brand details and preferences"
              delay={0.1}
            />
            <IconCard
              icon="🤖"
              title="AI Crafts Your Book"
              description="Our AI generates a custom brand book tailored to your needs"
              delay={0.3}
            />
            <IconCard
              icon="📩"
              title="Receive Your PDF"
              description="Get your professional brand book delivered to your email"
              delay={0.5}
            />
          </div>
        </div>
      </section>

      {/* Example Preview Section */}
      <section ref={examplesRef} id="examples" className="relative py-24 px-4 z-10">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-4">Example Brand Book</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">See what our AI can create for your brand</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="max-w-4xl mx-auto"
          >
            <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-white">
              <div className="h-96 md:h-[600px]">
                <PDFViewer source="/assets/sample-brandbook.pdf" className="h-full" />
              </div>
              <div className="absolute bottom-4 right-4">
                <Button
                  onClick={handleDownloadPDF}
                  className="bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 rounded-full shadow flex items-center gap-2"
                >
                  <Download size={16} />
                  Download PDF
                </Button>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Pricing Section */}
      <section ref={pricingRef} id="pricing" className="relative py-24 px-4 z-10">
        <div className="absolute inset-0 bg-white/70 backdrop-blur-sm z-0"></div>
        <div className="max-w-6xl mx-auto relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-4">Simple Pricing</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">Choose the option that works for you</p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <PricingCard
              title="Basic"
              price="$5"
              features={[
                "One custom brand book PDF",
                "Logo usage guidelines",
                "Color palette",
                "Typography recommendations",
                "Delivered within 30 minutes",
                "10 times cheaper than hiring a designer",
              ]}
              icon="📄"
              delay={0.1}
              onClick={() => {
                setFormValues({ ...formValues, pricing: "basic" })
                scrollToSection(formRef)
              }}
            />
            <PricingCard
              title="Pro"
              price="$10"
              features={[
                "3x unique brand book options",
                "Logo usage guidelines",
                "Typography recommendations",
                "Social media guidelines",
                "Delivered within 30 minutes",
                "10 times cheaper than hiring a designer",
              ]}
              icon="📚"
              highlighted={true}
              delay={0.3}
              onClick={() => {
                setFormValues({ ...formValues, pricing: "pro" })
                scrollToSection(formRef)
              }}
            />
          </div>
        </div>
      </section>

      {/* Form Section */}
      <section id="create" className="relative py-24 px-4 z-10">
        <div className="max-w-6xl mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-4">Create Your Brand Book</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">Fill in the details below to get started</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.8 }}
            className="max-w-2xl mx-auto"
          >
            <motion.div
              className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden"
              whileHover={{ boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.15)" }}
              transition={{ duration: 0.3 }}
            >
              <div className="p-2">
                <div className="h-2 w-full rounded-full bg-gradient-to-r from-purple-600 to-indigo-600"></div>
              </div>

              <form ref={formRef} onSubmit={(e) => e.preventDefault()} className="p-8">
                <div className="space-y-6">
                  <div>
                    <label htmlFor="brandName" className="block text-sm font-medium text-gray-700 mb-1">
                      Brand Name *
                    </label>
                    <Input
                      id="brandName"
                      name="brandName"
                      value={formValues.brandName}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                      placeholder="e.g., Acme Inc."
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="industry" className="block text-sm font-medium text-gray-700 mb-1">
                      Industry *
                    </label>
                    <Input
                      id="industry"
                      name="industry"
                      value={formValues.industry}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                      placeholder="e.g., Technology, Fashion, Food"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                      Brief Description *
                    </label>
                    <Textarea
                      id="description"
                      name="description"
                      value={formValues.description}
                      onChange={handleInputChange}
                      maxLength={280}
                      rows={3}
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 resize-none"
                      placeholder="Describe your brand in a few sentences..."
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1 text-right">{formValues.description.length}/280</p>
                  </div>

                  <div>
                    <label htmlFor="audience" className="block text-sm font-medium text-gray-700 mb-1">
                      Target Audience *
                    </label>
                    <Input
                      id="audience"
                      name="audience"
                      value={formValues.audience}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                      placeholder="e.g., Young professionals, Parents, Tech enthusiasts"
                      required
                    />
                  </div>

                  <div>
                    <label htmlFor="vibes" className="block text-sm font-medium text-gray-700 mb-1">
                      Brand Vibe / Personality * (max 5)
                    </label>
                    <div className="flex flex-wrap gap-2 mb-2">
                      {formValues.vibes.map((vibe, index) => (
                        <Badge
                          key={index}
                          className="flex items-center gap-1 bg-indigo-100 text-indigo-800 hover:bg-indigo-200 px-3 py-1 rounded-full"
                        >
                          {vibe}
                          <X size={14} className="cursor-pointer ml-1" onClick={() => removeVibe(index)} />
                        </Badge>
                      ))}
                    </div>
                    <Input
                      id="vibes"
                      value={currentVibe}
                      onChange={(e) => setCurrentVibe(e.target.value)}
                      onKeyDown={handleVibeKeyDown}
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                      placeholder="Type and press Enter to add (e.g., Modern, Playful, Elegant)"
                      disabled={formValues.vibes.length >= 5}
                    />
                    {formValues.vibes.length >= 5 && (
                      <p className="text-xs text-amber-600 mt-1">Maximum number of vibes reached</p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                      Email *
                    </label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formValues.email}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                      placeholder="Where we'll send your brand book"
                      required
                    />
                  </div>

                  <div className="space-y-3">
                    <p className="font-medium text-gray-700">Pricing Options *</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <label
                        className={cn(
                          "flex items-start p-4 rounded-xl border-2 cursor-pointer transition-all duration-200",
                          formValues.pricing === "basic"
                            ? "border-indigo-500 bg-indigo-50"
                            : "border-gray-200 hover:border-indigo-200",
                        )}
                      >
                        <input
                          type="radio"
                          name="pricing"
                          value="basic"
                          checked={formValues.pricing === "basic"}
                          onChange={() => setFormValues({ ...formValues, pricing: "basic" })}
                          className="sr-only"
                        />
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="text-lg font-medium">Basic</span>
                            {formValues.pricing === "basic" && <Check className="h-5 w-5 text-indigo-600" />}
                          </div>
                          <p className="text-2xl font-bold mt-1">$5</p>
                          <p className="text-sm text-gray-500 mt-1">One custom brand book PDF</p>
                        </div>
                      </label>

                      <label
                        className={cn(
                          "flex items-start p-4 rounded-xl border-2 cursor-pointer transition-all duration-200",
                          formValues.pricing === "pro"
                            ? "border-indigo-500 bg-indigo-50"
                            : "border-gray-200 hover:border-indigo-200",
                        )}
                      >
                        <input
                          type="radio"
                          name="pricing"
                          value="pro"
                          checked={formValues.pricing === "pro"}
                          onChange={() => setFormValues({ ...formValues, pricing: "pro" })}
                          className="sr-only"
                        />
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="text-lg font-medium">Pro</span>
                            {formValues.pricing === "pro" && <Check className="h-5 w-5 text-indigo-600" />}
                          </div>
                          <p className="text-2xl font-bold mt-1">$10</p>
                          <p className="text-sm text-gray-500 mt-1">Three unique brand book options</p>
                        </div>
                      </label>
                    </div>
                  </div>
                  
                  {/* Show progress or results if session exists */}
                  {sessionId && !Object.keys(pdfUrls).length && (
                    <BrandBookProgress
                      sessionId={sessionId}
                      status={sessionStatus}
                      email={formValues.email}
                      onComplete={(urls) => setPdfUrls(urls)}
                    />
                  )}
                  
                  {/* Show results if PDF is ready */}
                  {Object.keys(pdfUrls).length > 0 && (
                    <BrandBookResult
                      pdfUrls={pdfUrls}
                      onReset={handleReset}
                    />
                  )}
                  
                  {/* Show submit button only if not in progress */}
                  {!sessionId && (
                    <motion.div
                      whileHover={{ scale: isFormValid ? 1.02 : 1 }}
                      whileTap={{ scale: isFormValid ? 0.98 : 1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <Button
                        type="button"
                        disabled={!isFormValid || isSubmitting}
                        className={cn(
                          "w-full py-4 rounded-xl text-white font-medium text-lg transition-all duration-300",
                          isFormValid
                            ? "bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 shadow-lg hover:shadow-xl"
                            : "bg-gray-300 cursor-not-allowed",
                        )}
                        onClick={() => {
                          if (!isFormValid || isSubmitting) return;
                          // Redirect to Dodo Payments checkout based on plan
                          const paymentUrl = formValues.pricing === "pro"
                            ? "https://test.checkout.dodopayments.com/buy/pdt_mdO7h03oYagrTvqk0ndgb?quantity=1"
                            : "https://test.checkout.dodopayments.com/buy/pdt_6P8S9kN2HcvEbUsItW1N0?quantity=1";
                          window.location.href = paymentUrl;
                        }}
                      >
                        {isSubmitting ? (
                          <div className="flex items-center justify-center">
                            <div className="animate-spin mr-2 h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
                            <span>Processing...</span>
                          </div>
                        ) : (
                          "Generate My Brand Book"
                        )}
                      </Button>
                    </motion.div>
                  )}
                  
                  {/* Show error message if there's an error */}
                  {generationError && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="p-3 bg-red-50 border border-red-200 rounded-lg"
                    >
                      <p className="text-sm text-red-600">{generationError}</p>
                    </motion.div>
                  )}
                  
                  {/* Show message when submitting but no session yet */}
                  {isSubmitting && !sessionId && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="text-center text-sm text-gray-600 mt-4"
                    >
                      Initializing brand book generation...
                    </motion.div>
                  )}
                </div>
              </form>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* FAQ Section */}
      <section ref={faqRef} id="faq" className="relative py-24 px-4 z-10">
        <div className="absolute inset-0 bg-white/70 backdrop-blur-sm z-0"></div>
        <div className="max-w-3xl mx-auto relative z-10">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-4">Frequently Asked Questions</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">Everything you need to know about our service</p>
          </motion.div>

          <div className="space-y-4">
            <FAQItem
              question="How long does it take to receive my brand book?"
              answer="You'll receive your brand book within 30 minutes of purchase, though most are delivered within a few minutes."
              isActive={activeAccordion === 0}
              onClick={() => toggleAccordion(0)}
              delay={0.1}
            />
            <FAQItem
              question="Can I request revisions to my brand book?"
              answer="Yes! If you're not satisfied with your brand book, you can request one free revision with specific feedback. You can reachout to us on x.com @thoughtseedlabs to apply for a revision."
              isActive={activeAccordion === 1}
              onClick={() => toggleAccordion(1)}
              delay={0.2}
            />
            <FAQItem
              question="What's included in the brand book?"
              answer="Your brand book includes logo usage guidelines, color palette, typography recommendations, and brand voice guidelines. The Pro version includes the same with three different varients."
              isActive={activeAccordion === 2}
              onClick={() => toggleAccordion(2)}
              delay={0.3}
            />
            <FAQItem
              question="Do you create a logo as part of the brand book?"
              answer="We provide logo usage guidelines and recommendations, but we don't create a custom logo. We can suggest logo directions based on your brand identity."
              isActive={activeAccordion === 3}
              onClick={() => toggleAccordion(3)}
              delay={0.4}
            />
            <FAQItem
              question="What format will I receive my brand book in?"
              answer="Your brand book will be delivered as a high-resolution PDF that you can view, print, or share with your team and partners."
              isActive={activeAccordion === 4}
              onClick={() => toggleAccordion(4)}
              delay={0.5}
            />
            <FAQItem
              question="Can I use my brand book for commercial purposes?"
              answer="Once you receive your brand book, you have full commercial rights to use it for your business in any way you need."
              isActive={activeAccordion === 5}
              onClick={() => toggleAccordion(5)}
              delay={0.6}
            />
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative py-12 px-4 z-10 border-t border-gray-200 bg-white/80 backdrop-blur-sm">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-6 md:mb-0">
              <p className="text-xl font-bold bg-gradient-to-r from-purple-600 to-indigo-600 text-transparent bg-clip-text">
                AI Brand Book Creator
              </p>
            </div>

            <div className="flex flex-wrap justify-center gap-6 mb-6 md:mb-0">
              <a
                href="#create"
                onClick={(e) => {
                  e.preventDefault()
                  scrollToSection(formRef)
                }}
                className="text-gray-600 hover:text-indigo-600 transition-colors duration-200"
              >
                Create Now
              </a>
              <a
                href="#how-it-works"
                onClick={(e) => {
                  e.preventDefault()
                  scrollToSection(howItWorksRef)
                }}
                className="text-gray-600 hover:text-indigo-600 transition-colors duration-200"
              >
                How It Works
              </a>
              <a
                href="#faq"
                onClick={(e) => {
                  e.preventDefault()
                  scrollToSection(faqRef)
                }}
                className="text-gray-600 hover:text-indigo-600 transition-colors duration-200"
              >
                FAQ
              </a>
              <a href="#" className="text-gray-600 hover:text-indigo-600 transition-colors duration-200">
                Terms & Conditions
              </a>
              <a href="#" className="text-gray-600 hover:text-indigo-600 transition-colors duration-200">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-600 hover:text-indigo-600 transition-colors duration-200">
                Blog
              </a>
            </div>

            <div className="flex space-x-4">
              <motion.a
                href="https://twitter.com"
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.1, color: "#1DA1F2" }}
                className="text-gray-600 transition-colors duration-200"
              >
                <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723 10.054 10.054 0 01-3.127 1.184 4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                </svg>
              </motion.a>
            </div>
          </div>

          <div className="text-center text-sm text-gray-500 mt-8">
            &copy; {new Date().getFullYear()} AI Brand Book Creator. All rights reserved. Developed by <a href="https://thoughtseed.space" target="_blank" rel="noopener noreferrer" className="text-indigo-600 hover:underline">Thoughtseed Labs</a>.
          </div>
        </div>
      </footer>
    </main>
  )
}
