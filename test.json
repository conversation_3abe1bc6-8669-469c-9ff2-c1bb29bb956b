{
    "formData": {
        "brandName": "Acme Corp",
        "industry": "Technology",
        "description": "A leading provider of tech solutions.",
        "targetAudience": "Startups and SMBs",
        "vibes": [
            "innovative",
            "trustworthy",
            "modern"
        ],
        "email": "mohan<PERSON><EMAIL>",
        "plan": "basic"
    },
    "timestamp": 1747938011433,
    "status": {
        "pageConfigs": {
            "1": "pending",
            "2": "pending",
            "3": "pending"
        },
        "pages": {},
        "pdfBundles": {},
        "email": "pending"
    },
    "jobIds": [
        "img_05255c64-6c91-490f-9ec2-f0c993957794",
        "img_245199e9-f30c-47e2-be40-886c54375a49",
        "img_e210f485-5b05-4a9d-b75a-d4557fe1692d"
    ],
    "debug": {
        "debug": {
            "configResponse": [
                {
                    "page_number": 1,
                    "page_title": "Logo Guidelines",
                    "image_specs": {
                        "style": "clean graphic design layout, minimalist brand guidelines page, information design, optimized for clarity",
                        "resolution": "1536x1024",
                        "aspect_ratio": "3:2",
                        "orientation": "landscape",
                        "file_format": "PNG"
                    },
                    "layout_instructions": {
                        "grid": "implied clean grid system, well-spaced elements, balanced layout for 3:2 aspect ratio",
                        "background_color": "#FFFFFF",
                        "text_style": "Use 'Arial' font for labels and short descriptions, color '#222222'. Ensure text is legible and appropriately sized for 1536x1024 resolution."
                    },
                    "page_elements": [
                        {
                            "element_id": "page_header",
                            "type": "text_block",
                            "position_description": "top-left corner, moderate margin",
                            "size_description": "small but clear heading",
                            "content_prompt": "Display the text 'Logo Guidelines' styled as a small page header using Arial. Ensure readability."
                        },
                        {
                            "element_id": "primary_logo_display",
                            "type": "logo_display",
                            "position_description": "left side, top-middle section, prominent placement",
                            "size_description": "large focus, well-scaled for the canvas",
                            "content_prompt": "Generate and prominently display the primary Acme Corp logo—an abstract geometric 'A' mark in deep blue (#2176FF) with a clean modern sans-serif wordmark. Ensure high fidelity, clarity, and appropriate scale for 1536x1024. Add a small label 'Primary Logo' underneath in Arial."
                        },
                        {
                            "element_id": "logo_variations_display",
                            "type": "logo_display_multiple",
                            "position_description": "left side, below primary logo",
                            "size_description": "medium size, displayed clearly, potentially stacked if horizontal space is tight",
                            "content_prompt": "Generate and display logo variations: a horizontal lockup with the icon left of wordmark, a standalone icon, and a monochrome version in dark grey (#222222). Label each as 'Horizontal', 'Icon Only', 'Monochrome'. Distinct, clear, and well-spaced in Arial."
                        },
                        {
                            "element_id": "clear_space_diagram",
                            "type": "diagram",
                            "position_description": "right side, top section",
                            "size_description": "medium size, clear visual",
                            "content_prompt": "Illustrate the clear space rule: Show the primary Acme Corp logo surrounded by generous padding, indicated by subtle boundary lines using the height of the 'A' as the minimum clear space. Add label 'Clear Space' in Arial."
                        },
                        {
                            "element_id": "minimum_size_example",
                            "type": "logo_display_annotated",
                            "position_description": "right side, middle section, below clear space",
                            "size_description": "small but legible size",
                            "content_prompt": "Display the Acme Corp primary logo at its minimum legible size: 32px height for digital and 0.75in width for print. Clearly annotate '32px H' and '0.75in W'. Add label 'Minimum Size' in Arial."
                        },
                        {
                            "element_id": "incorrect_usage_display",
                            "type": "usage_example_grid",
                            "position_description": "bottom edge, spanning across middle section",
                            "size_description": "row of 2-3 small, clear examples",
                            "content_prompt": "Create a row of 3 clear visuals showing incorrect use of the Acme Corp logo: 1) stretched disproportionately, 2) changed to a non-brand color, 3) placed on a busy photo background. Use red 'X' marks. Distinct examples. Label section 'Incorrect Usage' in Arial."
                        }
                    ]
                },
                {
                    "page_number": 2,
                    "page_title": "Typography",
                    "image_specs": {
                        "style": "clean graphic design layout, minimalist brand guidelines page, typography focus, legible text",
                        "resolution": "1536x1024",
                        "aspect_ratio": "3:2",
                        "orientation": "landscape",
                        "file_format": "PNG"
                    },
                    "layout_instructions": {
                        "grid": "implied two-column layout, adjusted for 3:2 ratio, ensure comfortable reading space",
                        "background_color": "#FFFFFF",
                        "text_style": "Use 'Arial' font for labels, color '#222222'. Ensure all text elements are clearly legible at 1536x1024."
                    },
                    "page_elements": [
                        {
                            "element_id": "page_header",
                            "type": "text_block",
                            "position_description": "top-left corner, moderate margin",
                            "size_description": "small but clear heading",
                            "content_prompt": "Display the text 'Typography' styled as a small page header using Arial. Ensure readability."
                        },
                        {
                            "element_id": "primary_typeface_specimen",
                            "type": "font_specimen",
                            "position_description": "left column, occupying roughly top 60%",
                            "size_description": "clear display, readable font sizes",
                            "content_prompt": "Display Primary Typeface: Show 'Arial' name prominently. List available weights: Regular, Bold, Italic. Show alphabet sample (Aa Bb Cc...) and numbers (0-9) in Regular weight at a readable size. Use color '#222222'. Label section 'Primary Typeface'."
                        },
                        {
                            "element_id": "secondary_typeface_specimen",
                            "type": "font_specimen",
                            "position_description": "left column, occupying roughly bottom 40%",
                            "size_description": "medium display, readable font sizes",
                            "content_prompt": "Display Secondary Typeface: Show 'Arial' name. List available weights: Regular, Italic. Show alphabet sample (Aa Bb Cc...) in Regular weight at a readable size. Use color '#222222'. Label section 'Secondary Typeface'."
                        },
                        {
                            "element_id": "typographic_hierarchy_example",
                            "type": "text_block_styled",
                            "position_description": "right column, full height, clear vertical spacing",
                            "size_description": "column width, ensuring text wrap is natural",
                            "content_prompt": "Demonstrate typographic hierarchy for Acme Corp: Use Arial Bold 44pt for 'Heading Level 1', Arial Bold 28pt for 'Heading Level 2', and Arial Regular 18pt for a body paragraph sample ('Acme Corp delivers smart tech solutions for modern businesses.'). Use color '#222222'. Clearly distinguish each level. Label section 'Hierarchy Example'."
                        }
                    ]
                },
                {
                    "page_number": 3,
                    "page_title": "Color Palette & Graphics",
                    "image_specs": {
                        "style": "clean graphic design layout, minimalist brand guidelines page, color swatches, graphic elements display, clear labels",
                        "resolution": "1536x1024",
                        "aspect_ratio": "3:2",
                        "orientation": "landscape",
                        "file_format": "PNG"
                    },
                    "layout_instructions": {
                        "grid": "balanced layout, perhaps colors on top 2/3, graphics on bottom 1/3 or side-by-side if space allows",
                        "background_color": "#FFFFFF",
                        "text_style": "Use 'Arial' font for labels and HEX codes, color '#222222'. Ensure text legibility."
                    },
                    "page_elements": [
                        {
                            "element_id": "page_header",
                            "type": "text_block",
                            "position_description": "top-left corner, moderate margin",
                            "size_description": "small but clear heading",
                            "content_prompt": "Display the text 'Color & Graphics' styled as a small page header using Arial. Ensure readability."
                        },
                        {
                            "element_id": "color_palette_display",
                            "type": "color_palette_display",
                            "position_description": "top section or left two-thirds of page, organized grid/rows",
                            "size_description": "clearly visible swatches, readable labels",
                            "content_prompt": "Display the Acme Corp color palette. Primary: #2176FF (Blue), #222222 (Charcoal). Secondary: #FFD700 (Gold), #C0C0C0 (Silver). Accent: #00C896 (Mint). Arrange in neat rows. Display HEX code and name under each swatch in Arial, color '#222222'. Label sections 'Primary', 'Secondary', 'Accent'."
                        },
                        {
                            "element_id": "brand_pattern_swatch",
                            "type": "graphic_element_swatch",
                            "position_description": "bottom section or right third, top part",
                            "size_description": "medium square swatch, pattern visible",
                            "content_prompt": "Display a square swatch of the Acme Corp brand pattern: a geometric grid of diagonal lines in subtle blue (#2176FF) on white, evoking technology and progression. Detail should be crisp and visible. Add label 'Brand Pattern' in Arial."
                        },
                        {
                            "element_id": "iconography_style_example",
                            "type": "icon_display_grid",
                            "position_description": "bottom section or right third, bottom part",
                            "size_description": "row/grid of 2-3 small, clear icons",
                            "content_prompt": "Display 3 sample icons representing core technology elements: a simplified cloud, a circuit, and a shield. Style: thin-line, minimal, corners softly rounded, using #2176FF and #222222. Label 'Iconography Style' in Arial."
                        }
                    ]
                }
            ],
            "imagePrompts": [
                "Create a professional brand book page titled \"Logo Guidelines\".\nStyle: clean graphic design layout, minimalist brand guidelines page, information design, optimized for clarity.\nResolution: 1536x1024.\nAspect ratio: 3:2.\nOrientation: landscape.\nLayout: implied clean grid system, well-spaced elements, balanced layout for 3:2 aspect ratio.\nBackground color: #FFFFFF.\nText style: Use 'Arial' font for labels and short descriptions, color '#222222'. Ensure text is legible and appropriately sized for 1536x1024 resolution..\n\ntext_block (page_header): Display the text 'Logo Guidelines' styled as a small page header using Arial. Ensure readability.\nPosition: top-left corner, moderate margin.\nSize: small but clear heading.\n\nlogo_display (primary_logo_display): Generate and prominently display the primary Acme Corp logo—an abstract geometric 'A' mark in deep blue (#2176FF) with a clean modern sans-serif wordmark. Ensure high fidelity, clarity, and appropriate scale for 1536x1024. Add a small label 'Primary Logo' underneath in Arial.\nPosition: left side, top-middle section, prominent placement.\nSize: large focus, well-scaled for the canvas.\n\nlogo_display_multiple (logo_variations_display): Generate and display logo variations: a horizontal lockup with the icon left of wordmark, a standalone icon, and a monochrome version in dark grey (#222222). Label each as 'Horizontal', 'Icon Only', 'Monochrome'. Distinct, clear, and well-spaced in Arial.\nPosition: left side, below primary logo.\nSize: medium size, displayed clearly, potentially stacked if horizontal space is tight.\n\ndiagram (clear_space_diagram): Illustrate the clear space rule: Show the primary Acme Corp logo surrounded by generous padding, indicated by subtle boundary lines using the height of the 'A' as the minimum clear space. Add label 'Clear Space' in Arial.\nPosition: right side, top section.\nSize: medium size, clear visual.\n\nlogo_display_annotated (minimum_size_example): Display the Acme Corp primary logo at its minimum legible size: 32px height for digital and 0.75in width for print. Clearly annotate '32px H' and '0.75in W'. Add label 'Minimum Size' in Arial.\nPosition: right side, middle section, below clear space.\nSize: small but legible size.\n\nusage_example_grid (incorrect_usage_display): Create a row of 3 clear visuals showing incorrect use of the Acme Corp logo: 1) stretched disproportionately, 2) changed to a non-brand color, 3) placed on a busy photo background. Use red 'X' marks. Distinct examples. Label section 'Incorrect Usage' in Arial.\nPosition: bottom edge, spanning across middle section.\nSize: row of 2-3 small, clear examples.\n",
                "Create a professional brand book page titled \"Typography\".\nStyle: clean graphic design layout, minimalist brand guidelines page, typography focus, legible text.\nResolution: 1536x1024.\nAspect ratio: 3:2.\nOrientation: landscape.\nLayout: implied two-column layout, adjusted for 3:2 ratio, ensure comfortable reading space.\nBackground color: #FFFFFF.\nText style: Use 'Arial' font for labels, color '#222222'. Ensure all text elements are clearly legible at 1536x1024..\n\ntext_block (page_header): Display the text 'Typography' styled as a small page header using Arial. Ensure readability.\nPosition: top-left corner, moderate margin.\nSize: small but clear heading.\n\nfont_specimen (primary_typeface_specimen): Display Primary Typeface: Show 'Arial' name prominently. List available weights: Regular, Bold, Italic. Show alphabet sample (Aa Bb Cc...) and numbers (0-9) in Regular weight at a readable size. Use color '#222222'. Label section 'Primary Typeface'.\nPosition: left column, occupying roughly top 60%.\nSize: clear display, readable font sizes.\n\nfont_specimen (secondary_typeface_specimen): Display Secondary Typeface: Show 'Arial' name. List available weights: Regular, Italic. Show alphabet sample (Aa Bb Cc...) in Regular weight at a readable size. Use color '#222222'. Label section 'Secondary Typeface'.\nPosition: left column, occupying roughly bottom 40%.\nSize: medium display, readable font sizes.\n\ntext_block_styled (typographic_hierarchy_example): Demonstrate typographic hierarchy for Acme Corp: Use Arial Bold 44pt for 'Heading Level 1', Arial Bold 28pt for 'Heading Level 2', and Arial Regular 18pt for a body paragraph sample ('Acme Corp delivers smart tech solutions for modern businesses.'). Use color '#222222'. Clearly distinguish each level. Label section 'Hierarchy Example'.\nPosition: right column, full height, clear vertical spacing.\nSize: column width, ensuring text wrap is natural.\n",
                "Create a professional brand book page titled \"Color Palette & Graphics\".\nStyle: clean graphic design layout, minimalist brand guidelines page, color swatches, graphic elements display, clear labels.\nResolution: 1536x1024.\nAspect ratio: 3:2.\nOrientation: landscape.\nLayout: balanced layout, perhaps colors on top 2/3, graphics on bottom 1/3 or side-by-side if space allows.\nBackground color: #FFFFFF.\nText style: Use 'Arial' font for labels and HEX codes, color '#222222'. Ensure text legibility..\n\ntext_block (page_header): Display the text 'Color & Graphics' styled as a small page header using Arial. Ensure readability.\nPosition: top-left corner, moderate margin.\nSize: small but clear heading.\n\ncolor_palette_display (color_palette_display): Display the Acme Corp color palette. Primary: #2176FF (Blue), #222222 (Charcoal). Secondary: #FFD700 (Gold), #C0C0C0 (Silver). Accent: #00C896 (Mint). Arrange in neat rows. Display HEX code and name under each swatch in Arial, color '#222222'. Label sections 'Primary', 'Secondary', 'Accent'.\nPosition: top section or left two-thirds of page, organized grid/rows.\nSize: clearly visible swatches, readable labels.\n\ngraphic_element_swatch (brand_pattern_swatch): Display a square swatch of the Acme Corp brand pattern: a geometric grid of diagonal lines in subtle blue (#2176FF) on white, evoking technology and progression. Detail should be crisp and visible. Add label 'Brand Pattern' in Arial.\nPosition: bottom section or right third, top part.\nSize: medium square swatch, pattern visible.\n\nicon_display_grid (iconography_style_example): Display 3 sample icons representing core technology elements: a simplified cloud, a circuit, and a shield. Style: thin-line, minimal, corners softly rounded, using #2176FF and #222222. Label 'Iconography Style' in Arial.\nPosition: bottom section or right third, bottom part.\nSize: row/grid of 2-3 small, clear icons.\n"
            ],
            "timestamp": 1747938082643
        },
        "debug_page_1": {
            "timestamp": 1747938090532,
            "apiParams": {
                "prompt": "Create a professional brand book page titled \"Logo Guidelines\".\nStyle: clean graphic design layout, minimalist brand guidelines page, information design, optimized for clarity.\nResolution: 1536x1024.\nAspect ratio: 3:2.\nOrientation: landscape.\nLayout: implied clean grid system, well-spaced elements, balanced layout for 3:2 aspect ratio.\nBackground color: #FFFFFF.\nText style: Use 'Arial' font for labels and short descriptions, color '#222222'. Ensure text is legible and appropriately sized for 1536x1024 resolution..\n\ntext_block (page_header): Display the text 'Logo Guidelines' styled as a small page header using Arial. Ensure readability.\nPosition: top-left corner, moderate margin.\nSize: small but clear heading.\n\nlogo_display (primary_logo_display): Generate and prominently display the primary Acme Corp logo—an abstract geometric 'A' mark in deep blue (#2176FF) with a clean modern sans-serif wordmark. Ensure high fidelity, clarity, and appropriate scale for 1536x1024. Add a small label 'Primary Logo' underneath in Arial.\nPosition: left side, top-middle section, prominent placement.\nSize: large focus, well-scaled for the canvas.\n\nlogo_display_multiple (logo_variations_display): Generate and display logo variations: a horizontal lockup with the icon left of wordmark, a standalone icon, and a monochrome version in dark grey (#222222). Label each as 'Horizontal', 'Icon Only', 'Monochrome'. Distinct, clear, and well-spaced in Arial.\nPosition: left side, below primary logo.\nSize: medium size, displayed clearly, potentially stacked if horizontal space is tight.\n\ndiagram (clear_space_diagram): Illustrate the clear space rule: Show the primary Acme Corp logo surrounded by generous padding, indicated by subtle boundary lines using the height of the 'A' as the minimum clear space. Add label 'Clear Space' in Arial.\nPosition: right side, top section.\nSize: medium size, clear visual.\n\nlogo_display_annotated (minimum_size_example): Display the Acme Corp primary logo at its minimum legible size: 32px height for digital and 0.75in width for print. Clearly annotate '32px H' and '0.75in W'. Add label 'Minimum Size' in Arial.\nPosition: right side, middle section, below clear space.\nSize: small but legible size.\n\nusage_example_grid (incorrect_usage_display): Create a row of 3 clear visuals showing incorrect use of the Acme Corp logo: 1) stretched disproportionately, 2) changed to a non-brand color, 3) placed on a busy photo background. Use red 'X' marks. Distinct examples. Label section 'Incorrect Usage' in Arial.\nPosition: bottom edge, spanning across middle section.\nSize: row of 2-3 small, clear examples.\n",
                "model": "gpt-image-1",
                "n": 1,
                "size": "1536x1024",
                "quality": "high",
                "background": "transparent"
            },
            "incomingImageParams": {
                "model": "gpt-image-1",
                "size": "1536x1024",
                "quality": "high",
                "output_format": "png",
                "background": "transparent"
            },
            "apiKey": "[PRESENT]"
        },
        "debug_page_1_response": {
            "timestamp": 1747938140489,
            "status": "success",
            "has_data": true,
            "data_type": "b64_json"
        },
        "debug_page_2": {
            "timestamp": 1747938143401,
            "apiParams": {
                "prompt": "Create a professional brand book page titled \"Typography\".\nStyle: clean graphic design layout, minimalist brand guidelines page, typography focus, legible text.\nResolution: 1536x1024.\nAspect ratio: 3:2.\nOrientation: landscape.\nLayout: implied two-column layout, adjusted for 3:2 ratio, ensure comfortable reading space.\nBackground color: #FFFFFF.\nText style: Use 'Arial' font for labels, color '#222222'. Ensure all text elements are clearly legible at 1536x1024..\n\ntext_block (page_header): Display the text 'Typography' styled as a small page header using Arial. Ensure readability.\nPosition: top-left corner, moderate margin.\nSize: small but clear heading.\n\nfont_specimen (primary_typeface_specimen): Display Primary Typeface: Show 'Arial' name prominently. List available weights: Regular, Bold, Italic. Show alphabet sample (Aa Bb Cc...) and numbers (0-9) in Regular weight at a readable size. Use color '#222222'. Label section 'Primary Typeface'.\nPosition: left column, occupying roughly top 60%.\nSize: clear display, readable font sizes.\n\nfont_specimen (secondary_typeface_specimen): Display Secondary Typeface: Show 'Arial' name. List available weights: Regular, Italic. Show alphabet sample (Aa Bb Cc...) in Regular weight at a readable size. Use color '#222222'. Label section 'Secondary Typeface'.\nPosition: left column, occupying roughly bottom 40%.\nSize: medium display, readable font sizes.\n\ntext_block_styled (typographic_hierarchy_example): Demonstrate typographic hierarchy for Acme Corp: Use Arial Bold 44pt for 'Heading Level 1', Arial Bold 28pt for 'Heading Level 2', and Arial Regular 18pt for a body paragraph sample ('Acme Corp delivers smart tech solutions for modern businesses.'). Use color '#222222'. Clearly distinguish each level. Label section 'Hierarchy Example'.\nPosition: right column, full height, clear vertical spacing.\nSize: column width, ensuring text wrap is natural.\n",
                "model": "gpt-image-1",
                "n": 1,
                "size": "1536x1024",
                "quality": "high",
                "background": "transparent"
            },
            "incomingImageParams": {
                "model": "gpt-image-1",
                "size": "1536x1024",
                "quality": "high",
                "output_format": "png",
                "background": "transparent"
            },
            "apiKey": "[PRESENT]"
        }
    }
}


{
    "configs": [
        {
            "pageNumber": 1,
            "prompt": "Create a professional brand book page titled \"Logo Guidelines\".\nStyle: clean graphic design layout, minimalist brand guidelines page, information design, optimized for clarity.\nResolution: 1536x1024.\nAspect ratio: 3:2.\nOrientation: landscape.\nLayout: implied clean grid system, well-spaced elements, balanced layout for 3:2 aspect ratio.\nBackground color: #FFFFFF.\nText style: Use 'Inter' font for labels and short descriptions, color '#232323'. Ensure text is legible and appropriately sized for 1536x1024 resolution..\n\ntext_block (page_header): Display the text 'Logo Guidelines' styled as a small page header using Inter font. Ensure readability and clarity at the top left margin.\nPosition: top-left corner, moderate margin.\nSize: small but clear heading.\n\nlogo_display (primary_logo_display): Display the primary Acme Corp logo—a modern geometric logomark combining interlocking digital triangles and a sharp, custom Inter wordmark in bold. Colors: #1657FF (blue) and #232323 (deep gray). Ensure clarity and high fidelity, centered at 60% width on a white background. Add a small label 'Primary Logo' below in Inter SemiBold.\nPosition: left side, top-middle section, prominent placement.\nSize: large focus, well-scaled for the canvas.\n\nlogo_display_multiple (logo_variations_display): Display logo variations: (1) 'Horizontal'—logomark left of wordmark; (2) 'Vertical'—logomark above wordmark; (3) 'Icon Only'—interlocking triangles only. Use brand colors, Inter font for all labels. Space each variation for distinctness, labeled appropriately beneath each.\nPosition: left side, below primary logo.\nSize: medium size, displayed clearly, potentially stacked if horizontal space is tight.\n\ndiagram (clear_space_diagram): Illustrate the clear space rule: show primary logo with a subtle outline or margin, using the height of the 'A' in Inter as the minimum spacing on all sides. Add annotation lines and label 'Clear Space' above or beside, ensuring understanding and elegance.\nPosition: right side, top section.\nSize: medium size, clear visual.\n\nlogo_display_annotated (minimum_size_example): Show the Acme Corp primary logo at minimum digital size: 32px height, and minimum print width: 0.75 in. Use thin hairlines for scale, labels '32px H' and '0.75in W' clearly in Inter Light. Add 'Minimum Size' label above.\nPosition: right side, middle section, below clear space.\nSize: small but legible size.\n\nusage_example_grid (incorrect_usage_display): Create 3 examples of incorrect logo use for Acme Corp: (1) Distorted proportions, (2) Wrong colors (e.g. using red instead of blue/gray), (3) Placed on low-contrast background. Each example marked with a bold red 'X'. Label below this row: 'Incorrect Usage' in Inter Bold.\nPosition: bottom edge, spanning across middle section.\nSize: row of 3 small, clear examples.\n",
            "imageParams": {
                "model": "gpt-image-1",
                "size": "1536x1024",
                "quality": "high",
                "output_format": "png",
                "background": "transparent"
            }
        },
        {
            "pageNumber": 2,
            "prompt": "Create a professional brand book page titled \"Typography\".\nStyle: clean graphic design layout, minimalist brand guidelines page, typography focus, legible text.\nResolution: 1536x1024.\nAspect ratio: 3:2.\nOrientation: landscape.\nLayout: implied two-column layout, adjusted for 3:2 ratio, ensure comfortable reading space.\nBackground color: #FFFFFF.\nText style: Use 'Inter' font for labels, color '#232323'. Ensure all text elements are clearly legible at 1536x1024..\n\ntext_block (page_header): Display the text 'Typography' styled as a small page header using Inter font. Ensure readability and clean margin.\nPosition: top-left corner, moderate margin.\nSize: small but clear heading.\n\nfont_specimen (primary_typeface_specimen): Display Primary Typeface: 'Inter'. Available weights: Light, Regular, Medium, SemiBold, Bold. Show alphabet: Aa Bb Cc Dd Ee Ff Gg, and numbers 0 1 2 3 4 5 6 7 8 9 in Regular at large, readable size. Use #232323 text color. Label section 'Primary Typeface' in bold Inter.\nPosition: left column, occupying roughly top 60%.\nSize: clear display, readable font sizes.\n\nfont_specimen (secondary_typeface_specimen): Display Secondary Typeface: 'Roboto'. Available weights: Light, Regular, Medium, Bold. Present alphabet: Aa Bb Cc Dd Ee Ff Gg in Regular at readable size. Use #232323 color. Label as 'Secondary Typeface' in Inter Bold.\nPosition: left column, occupying roughly bottom 40%.\nSize: medium display, readable font sizes.\n\ntext_block_styled (typographic_hierarchy_example): Demonstrate typographic hierarchy for Acme Corp: Heading 1 — Inter Bold, 56pt; Heading 2 — Inter SemiBold, 32pt; Body text — Inter Regular, 18pt. Example text: 'Acme Leads Innovation', 'Solutions for Startups', and a short body paragraph. Use #232323 color for all. Ensure vertical space between each. Label this column 'Hierarchy Example'.\nPosition: right column, full height, clear vertical spacing.\nSize: column width, ensuring text wrap is natural.\n",
            "imageParams": {
                "model": "gpt-image-1",
                "size": "1536x1024",
                "quality": "high",
                "output_format": "png",
                "background": "transparent"
            }
        },
        {
            "pageNumber": 3,
            "prompt": "Create a professional brand book page titled \"Color Palette & Graphics\".\nStyle: clean graphic design layout, minimalist brand guidelines page, color swatches, graphic elements display, clear labels.\nResolution: 1536x1024.\nAspect ratio: 3:2.\nOrientation: landscape.\nLayout: balanced layout, perhaps colors on top 2/3, graphics on bottom 1/3 or side-by-side if space allows.\nBackground color: #FFFFFF.\nText style: Use 'Inter' font for labels and HEX codes, color '#232323'. Ensure text legibility..\n\ntext_block (page_header): Display the text 'Color & Graphics' styled as a small page header using Inter font. Ensure readability and balanced top margin.\nPosition: top-left corner, moderate margin.\nSize: small but clear heading.\n\ncolor_palette_display (color_palette_display): Display Acme Corp brand color palette: Primary Colors—#1657FF (Acme Blue), #232323 (Acme Black). Secondary Colors—#6A7A8C (Slate Gray), #E6F0FA (Light Blue). Accent Color—#FFD966 (Gold Accent). Arrange swatches in a grid, each with HEX and name below in Inter. Clearly segment 'Primary', 'Secondary', 'Accent'.\nPosition: top section or left two-thirds of page, organized grid/rows.\nSize: clearly visible swatches, readable labels.\n\ngraphic_element_swatch (brand_pattern_swatch): Show a square swatch of Acme Corp's signature pattern: interwoven geometric triangles in subtle tints of #1657FF and #6A7A8C on white. Ensure detail is visible at 256x256px. Label 'Brand Pattern' below in Inter.\nPosition: bottom section or right third, top part.\nSize: medium square swatch, pattern visible.\n\nicon_display_grid (iconography_style_example): Display 3 sample icons—line-based, geometric, modern—demonstrating Acme Corp's icon style: simple, slight rounded corners, using #1657FF lines. Space with white between. Label 'Iconography Style' below in Inter SemiBold.\nPosition: bottom section or right third, bottom part.\nSize: row/grid of 3 small, clear icons.\n",
            "imageParams": {
                "model": "gpt-image-1",
                "size": "1536x1024",
                "quality": "high",
                "output_format": "png",
                "background": "transparent"
            }
        }
    ]
}
