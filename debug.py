#!/usr/bin/env python
import requests
import json
import sys
import argparse

def fetch_debug_data(session_id):
    """Fetch debug data for a specific session"""
    url = f"https://mybrandbook-backend.thoughtseedlabs.workers.dev/debug?sessionId={session_id}"
    response = requests.get(url)
    
    if response.status_code == 200:
        data = response.json()
        return data
    else:
        print(f"Error: {response.status_code}")
        print(response.text)
        return None

def main():
    parser = argparse.ArgumentParser(description="Debug tool for MyBrandbookAI")
    parser.add_argument("session_id", help="Session ID to fetch debug data for")
    parser.add_argument("--prompt", action="store_true", help="Show full prompt text")
    args = parser.parse_args()
    
    data = fetch_debug_data(args.session_id)
    if not data:
        sys.exit(1)
    
    print("\n=== Debug Data ===")
    print(f"Timestamp: {data.get('timestamp')}")
    
    # Print image prompts info
    prompts = data.get('imagePrompts', [])
    if prompts:
        print(f"\nFound {len(prompts)} image prompts")
        for i, prompt in enumerate(prompts):
            if args.prompt:
                # Show full prompt
                print(f"\n--- Prompt {i+1} ---")
                print(prompt)
            else:
                # Show truncated prompt
                print(f"\n--- Prompt {i+1} (truncated) ---")
                print(f"{prompt[:200]}...")
    
    # Print configs info
    configs = data.get('configResponse', [])
    if configs:
        print(f"\nFound {len(configs)} configs")
        
        for i, config in enumerate(configs):
            print(f"\n--- Config {i+1} Structure ---")
            
            # Extract key info from the config for brevity
            if isinstance(config, dict) and 'brand_book_page_config' in config:
                page_config = config['brand_book_page_config']
                print(f"Page Number: {page_config.get('page_number')}")
                print(f"Page Title: {page_config.get('page_title')}")
                
                # Print image specs
                if 'image_specs' in page_config:
                    print("\nImage Specs:")
                    for key, value in page_config['image_specs'].items():
                        print(f"  {key}: {value}")
                
                # Print number of page elements
                if 'page_elements' in page_config:
                    elements = page_config['page_elements']
                    print(f"\nPage Elements: {len(elements)}")
                    for j, elem in enumerate(elements):
                        print(f"  Element {j+1}: {elem.get('type')} - {elem.get('element_id')}")
            else:
                # Fallback for unexpected config structure
                print(json.dumps(config, indent=2))

if __name__ == "__main__":
    main()
