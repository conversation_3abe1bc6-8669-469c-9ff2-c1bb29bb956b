# MyB<PERSON>bookAI

MyBrandbookAI is an AI-powered brand book generator that creates professional brand guidelines using advanced AI models. It transforms simple brand information into comprehensive, visually appealing brand books.

## 🚀 Features

- **AI-Powered Brand Book Generation**: Create professional brand books with just a few inputs
- **Structured Templates**: Highly detailed JSON templates for consistent, high-quality output
- **Customizable Designs**: Generate brand books that match your brand's personality
- **Real-time Progress Tracking**: Monitor the generation process in real-time
- **PDF Delivery**: Receive your brand book as a downloadable PDF
- **Email Delivery**: Get your brand book sent directly to your email

## 🏗️ Architecture

The application consists of two main parts:

### Frontend
- Next.js React application with a modern, responsive UI
- Framer Motion for smooth animations
- Tailwind CSS for styling
- TypeScript for type safety

### Backend
- Cloudflare Workers for serverless API endpoints
- OpenAI GPT-4.1 for generating brand book configurations
- OpenAI gpt-image-1 for image generation
- KV storage for templates and session management
- R2 storage for PDF files
- Resend for email delivery

## 🔧 Technical Implementation

### Brand Book Generation Process

1. **Form Submission**: User submits brand information (name, industry, description, etc.)
2. **Session Creation**: Backend creates a unique session for tracking the generation process
3. **Config Generation**: GPT-4.1 fills in template placeholders with brand-specific content
4. **Image Generation**: gpt-image-1 creates brand book page images based on the configurations
5. **PDF Creation**: Images are compiled into a PDF brand book
6. **Delivery**: The PDF is stored in R2 and sent to the user's email

### Template System

The application uses a sophisticated template system with JSON configurations that define:

- Page layouts and elements
- Typography and color schemes
- Logo placement and variations
- Design guidelines and rules

These templates contain placeholders that are filled by GPT-4.1 based on the user's brand information, creating customized brand books.

## 📋 Project Structure

```
mybrandbook/
├── app/                  # Next.js app directory
├── components/           # React components
├── lib/                  # Utility functions
├── public/               # Static assets
├── styles/               # Global styles
└── backend/              # Backend API
    ├── src/              # Source code
    │   ├── index.ts      # Main API handler
    │   └── templates/    # Brand book templates
    └── wrangler.toml     # Cloudflare Workers config
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm or pnpm
- Cloudflare account (for backend deployment)
- OpenAI API key
- Resend API key (for email delivery)
- DodoPayments account (for payments)

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/mybrandbookAI.git
   cd mybrandbookAI
   ```

2. Install dependencies:
   ```bash
   pnpm install
   ```

3. Set up environment variables:
   Create a `.env.local` file in the root directory with:
   ```
   NEXT_PUBLIC_API_URL=your_api_url
   ```

4. For backend setup:
   ```bash
   cd backend
   pnpm install
   ```

5. Configure Cloudflare Workers:
   Update `wrangler.toml` with your account ID and KV namespace IDs

### Development

1. Run the frontend development server:
   ```bash
   pnpm dev
   ```

2. Run the backend locally:
   ```bash
   cd backend
   pnpm dev
   ```

### Deployment

1. Deploy the frontend to Vercel:
   ```bash
   pnpm build
   vercel deploy
   ```

2. Deploy the backend to Cloudflare Workers:
   ```bash
   cd backend
   pnpm deploy
   ```

## 📝 License

This project is proprietary and confidential. All rights reserved.

## 🙏 Acknowledgements

- [OpenAI](https://openai.com/) for GPT-4.1 and gpt-image-1
- [Cloudflare Workers](https://workers.cloudflare.com/) for serverless infrastructure
- [Next.js](https://nextjs.org/) for the frontend framework
- [Tailwind CSS](https://tailwindcss.com/) for styling
- [Framer Motion](https://www.framer.com/motion/) for animations
