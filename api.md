curl --location 'https://mybrandbook-backend.thoughtseedlabs.workers.dev/submit-form' \
--header 'Content-Type: application/json' \
--data-raw '{
  "brandName": "Acme Corp",
  "industry": "Technology",
  "description": "A leading provider of tech solutions.",
  "targetAudience": "Startups and SMBs",
  "vibes": ["innovative", "trustworthy", "modern"],
  "email": "<EMAIL>",
  "plan": "basic"
}'

curl --location 'https://mybrandbook-backend.thoughtseedlabs.workers.dev/webhook' \
--header 'Content-Type: application/json' \
--header 'webhook-id: teststringId' \
--header 'webhook-signature: test-signature' \
--header 'webhook-timestamp: **********' \
--data '{
  "type": "payment.succeeded",
  "data": {
    "metadata": {
      "sessionId": "sess_2c29d2755ab0"
    },
    "amount": 1000,
    "currency": "USD"
  }
}'

curl --location 'https://mybrandbook-backend.thoughtseedlabs.workers.dev/generate-config' \
--header 'Content-Type: application/json' \
--data-raw '{
  "formInput": {
    "brandName": "Acme Corp",
    "industry": "Technology",
    "description": "A leading provider of tech solutions.",
    "targetAudience": "Startups and SMBs",
    "vibes": ["innovative", "trustworthy", "modern"],
    "email": "<EMAIL>",
    "plan": "basic"
  },
  "plan": "basic"
}'

curl --location 'https://mybrandbook-backend.thoughtseedlabs.workers.dev/generate-brandbook' \
--header 'Content-Type: application/json' \
--data '{
  "configs": [
    {
      "pageNumber": 1,
      "prompt": "Create a professional brand book page titled \"Logo Guidelines\". \nStyle: clean graphic design layout, minimalist brand guidelines page, information design, optimized for clarity. \nResolution: 1536x1024. \nAspect ratio: 3:2. \nOrientation: landscape.\nLayout: implied clean grid system, well-spaced elements, balanced layout for 3:2 aspect ratio. \nBackground color: #FFFFFF. \nText style: Use '\''Satoshi'\'' font for labels and short descriptions, color '\''#0E1B2F'\''. Ensure text is legible and appropriately sized for 1536x1024 resolution.. \n\ntext_block (page_header): Display the text '\''Logo Guidelines'\'' styled as a small page header using Satoshi font. Ensure readability. \nPosition: top-left corner, moderate margin. \nSize: small but clear heading.\n\nlogo_display (primary_logo_display): Display the primary Acme Corp logo—an abstract geometric monogram '\''A'\'' with a digital pixel grid background in blue (#1565C0) and charcoal (#0E1B2F)—centered and prominent on a white (#FFFFFF) canvas. Add a small label '\''Primary Logo'\'' underneath in Satoshi Bold. \nPosition: left side, top-middle section, prominent placement. \nSize: large focus, well-scaled for the canvas.\n\nlogo_display_multiple (logo_variations_display): Show Acme Corp logo variations: (1) Horizontal lockup—'\''A'\'' monogram with '\''Acme Corp'\'' text to the right; (2) Icon Only—the '\''A'\'' monogram in blue; (3) Single-color version in #0E1B2F. Label each as '\''Horizontal'\'', '\''Icon Only'\'', '\''Single-Color'\''. Ensure each is distinct and spaced with clarity. \nPosition: left side, below primary logo. \nSize: medium size, displayed clearly, potentially stacked if horizontal space is tight.\n\ndiagram (clear_space_diagram): Illustrate the clear space around the Acme Corp primary logo: show the logo surrounded by a subtle dotted boundary demonstrating a minimum clear space equal to the height of the letter '\''A'\''. Label the boundary area as '\''Clear Space'\'' using Satoshi. \nPosition: right side, top section. \nSize: medium size, clear visual.\n\nlogo_display_annotated (minimum_size_example): Show the Acme Corp logo at minimum size: digital minimum 32px high, print minimum 0.75in wide. Add annotation lines and dimension labels ('\''32px H'\'', '\''0.75in W'\''). Use Satoshi font for the '\''Minimum Size'\'' label. \nPosition: right side, middle section, below clear space. \nSize: small but legible size.\n\nusage_example_grid (incorrect_usage_display): Create a row showing incorrect uses: (1) logo in off-brand colors (red or green), (2) logo stretched/distorted horizontally, (3) logo with decorative shadow or effects. Overlay each example with a clear red '\''X'\''. Label this area '\''Incorrect Usage'\'' in Satoshi. \nPosition: bottom edge, spanning across middle section. \nSize: row of 2-3 small, clear examples.\n",
      "imageParams": {
        "model": "gpt-image-1",
        "size": "1536x1024",
        "quality": "high",
        "style": "photorealistic",
        "output_format": "png", // Changed from response_format as it's not supported for gpt-image-1
        "background": "transparent"
      }
    },
    {
      "pageNumber": 2,
      "prompt": "Create a professional brand book page titled \"Typography\". \nStyle: clean graphic design layout, minimalist brand guidelines page, typography focus, legible text. \nResolution: 1536x1024. \nAspect ratio: 3:2. \nOrientation: landscape.\nLayout: implied two-column layout, adjusted for 3:2 ratio, ensure comfortable reading space. \nBackground color: #FFFFFF. \nText style: Use '\''Satoshi'\'' font for labels, color '\''#0E1B2F'\''. Ensure all text elements are clearly legible at 1536x1024.. \n\ntext_block (page_header): Display the text '\''Typography'\'' styled as a small page header using Satoshi font. Ensure readability. \nPosition: top-left corner, moderate margin. \nSize: small but clear heading.\n\nfont_specimen (primary_typeface_specimen): Display Primary Typeface: Show '\''Satoshi'\'' name prominently. Available weights: Light, Regular, Medium, Bold. Show alphabet (Aa Bb Cc Dd Ee...) and numbers (0 1 2 3 4 5 6 7 8 9) in Regular at a large, clear size. Use #0E1B2F for all text. Label '\''Primary Typeface'\'' with clarity. \nPosition: left column, occupying roughly top 60%. \nSize: clear display, readable font sizes.\n\nfont_specimen (secondary_typeface_specimen): Display Secondary Typeface: Show '\''Inter'\'' name. Available weights: Regular, Bold. Show alphabet (Aa Bb Cc Dd Ee...) in Regular at a comfortable size. Use #0E1B2F for all text. Label this area '\''Secondary Typeface'\''. \nPosition: left column, occupying roughly bottom 40%. \nSize: medium display, readable font sizes.\n\ntext_block_styled (typographic_hierarchy_example): Demonstrate typographic hierarchy for Acme Corp: Heading Level 1—Satoshi Bold 48pt, Heading Level 2—Satoshi Medium 32pt, Body—Inter Regular 20pt. Use #0E1B2F. Show a short Heading 1, a Heading 2 below, and a two-line body paragraph, clearly styled and vertically spaced. Label as '\''Hierarchy Example'\''. \nPosition: right column, full height, clear vertical spacing. \nSize: column width, ensuring text wrap is natural.\n",
      "imageParams": {
        "model": "gpt-image-1",
        "size": "1536x1024",
        "quality": "high",
        "style": "photorealistic",
        "output_format": "png", // Changed from response_format as it's not supported for gpt-image-1
        "background": "transparent"
      }
    },
    {
      "pageNumber": 3,
      "prompt": "Create a professional brand book page titled \"Color Palette & Graphics\". \nStyle: clean graphic design layout, minimalist brand guidelines page, color swatches, graphic elements display, clear labels. \nResolution: 1536x1024. \nAspect ratio: 3:2. \nOrientation: landscape.\nLayout: balanced layout, perhaps colors on top 2/3, graphics on bottom 1/3 or side-by-side if space allows. \nBackground color: #FFFFFF. \nText style: Use '\''Satoshi'\'' font for labels and HEX codes, color '\''#0E1B2F'\''. Ensure text legibility.. \n\ntext_block (page_header): Display the text '\''Color & Graphics'\'' styled as a small page header using Satoshi font. Ensure readability. \nPosition: top-left corner, moderate margin. \nSize: small but clear heading.\n\ncolor_palette_display (color_palette_display): Show Acme Corp'\''s color palette: Primary—#1565C0 (Electric Blue), #0E1B2F (Charcoal). Secondary—#90CAF9 (Sky Blue), #F5F5F5 (Light Gray). Accent—#FFC233 (Vivid Yellow). Arrange swatches in orderly rows, with HEX codes and color names labelled using Satoshi in #0E1B2F. Group under '\''Primary'\'', '\''Secondary'\'', '\''Accent'\''. \nPosition: top section or left two-thirds of page, organized grid/rows. \nSize: clearly visible swatches, readable labels.\n\ngraphic_element_swatch (brand_pattern_swatch): Display a square swatch featuring the Acme Corp brand pattern—a geometric array of interlocking '\''A'\'' monograms in Electric Blue (#1565C0) and Sky Blue (#90CAF9) on Light Gray (#F5F5F5). Ensure the pattern is crisp, contemporary, and visible at this size. Label '\''Brand Pattern'\'' underneath. \nPosition: bottom section or right third, top part. \nSize: medium square swatch, pattern visible.\n\nicon_display_grid (iconography_style_example): Show 3 sample Acme Corp icons: outlined, rounded-corner style, line icons at 2.5pt stroke in #1565C0. Icons should visually suggest technology and innovation, such as a cloud, circuit, and shield. Label section '\''Iconography Style'\'' in Satoshi. \nPosition: bottom section or right third, bottom part. \nSize: row/grid of 2-3 small, clear icons.\n",
      "imageParams": {
        "model": "gpt-image-1",
        "size": "1536x1024",
        "quality": "high",
        "style": "photorealistic",
        "output_format": "png", // Changed from response_format as it's not supported for gpt-image-1
        "background": "transparent"
      }
    }
  ]
}'

curl --location 'https://mybrandbook-backend.thoughtseedlabs.workers.dev/session/sess_83bb8be65448'

