"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface BrandBookCarouselProps {
  currentSlide: number
  setCurrentSlide: (slide: number) => void
}

const slides = [
  {
    id: 1,
    title: "Tech Startup Brand Book",
    image: "/placeholder.svg?key=y0t2d",
  },
  {
    id: 2,
    title: "Coffee Shop Brand Book",
    image: "/placeholder.svg?key=dwb30",
  },
  {
    id: 3,
    title: "Fashion Boutique Brand Book",
    image: "/placeholder.svg?key=79ccj",
  },
]

export function BrandBookCarousel({ currentSlide, setCurrentSlide }: BrandBookCarouselProps) {
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate loading of images
    const timer = setTimeout(() => {
      setLoading(false)
    }, 1000)
    return () => clearTimeout(timer)
  }, [])

  const nextSlide = () => {
    setCurrentSlide((currentSlide + 1) % slides.length)
  }

  const prevSlide = () => {
    setCurrentSlide((currentSlide - 1 + slides.length) % slides.length)
  }

  return (
    <div className="relative rounded-2xl overflow-hidden shadow-2xl">
      {loading ? (
        <div className="h-96 bg-gray-100 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <div className="h-12 w-12 rounded-full border-4 border-t-indigo-500 border-r-transparent border-b-indigo-300 border-l-indigo-600 animate-spin"></div>
            <p className="mt-4 text-gray-600">Loading preview...</p>
          </div>
        </div>
      ) : (
        <>
          <div className="relative h-96 overflow-hidden">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentSlide}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
                className="absolute inset-0"
              >
                <img
                  src={slides[currentSlide].image || "/placeholder.svg"}
                  alt={slides[currentSlide].title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6">
                  <h3 className="text-white text-xl font-bold">{slides[currentSlide].title}</h3>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          <button
            onClick={prevSlide}
            className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-all duration-200"
            aria-label="Previous slide"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
          <button
            onClick={nextSlide}
            className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white rounded-full p-2 shadow-lg transition-all duration-200"
            aria-label="Next slide"
          >
            <ChevronRight className="h-6 w-6" />
          </button>

          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2">
            {slides.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`h-2 rounded-full transition-all duration-200 ${
                  currentSlide === index ? "w-8 bg-white" : "w-2 bg-white/50"
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </>
      )}
    </div>
  )
}
