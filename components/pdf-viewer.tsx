"use client"

import { useEffect, useRef, useState } from "react"
import { Download } from "lucide-react"
import { Button } from "@/components/ui/button"

interface PDFViewerProps {
  source: string
  className?: string
  showDownload?: boolean
}

export function PDFViewer({ source, className, showDownload = false }: PDFViewerProps) {
  const [isLoading, setIsLoading] = useState(true)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  useEffect(() => {
    const iframe = iframeRef.current
    if (iframe) {
      iframe.onload = () => {
        setIsLoading(false)
      }
    }
  }, [])

  const handleDownload = () => {
    const link = document.createElement("a")
    link.href = source
    link.download = source.split("/").pop() || "document.pdf"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="flex flex-col items-center">
            <div className="h-12 w-12 rounded-full border-4 border-t-indigo-500 border-r-transparent border-b-indigo-300 border-l-indigo-600 animate-spin"></div>
            <p className="mt-4 text-gray-600">Loading preview...</p>
          </div>
        </div>
      )}
      <iframe
        ref={iframeRef}
        src={source}
        className={`w-full h-full ${isLoading ? "opacity-0" : "opacity-100"}`}
        title="PDF Viewer"
      />
      {showDownload && !isLoading && (
        <div className="absolute bottom-4 right-4">
          <Button
            onClick={handleDownload}
            className="bg-white hover:bg-gray-100 text-gray-800 font-semibold py-2 px-4 rounded-full shadow flex items-center gap-2"
          >
            <Download size={16} />
            Download PDF
          </Button>
        </div>
      )}
    </div>
  )
}
