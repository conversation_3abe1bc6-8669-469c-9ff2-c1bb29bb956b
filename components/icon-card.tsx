"use client"

import { motion } from "framer-motion"

interface IconCardProps {
  icon: string
  title: string
  description: string
  delay?: number
}

export function IconCard({ icon, title, description, delay = 0 }: IconCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, delay }}
      className="flex flex-col items-center text-center"
    >
      <motion.div
        className="text-4xl md:text-5xl mb-4 bg-white rounded-full h-20 w-20 flex items-center justify-center shadow-md"
        whileHover={{ scale: 1.1, rotate: 5 }}
        transition={{ type: "spring", stiffness: 300 }}
      >
        {icon}
      </motion.div>
      <h3 className="text-xl md:text-2xl font-bold mb-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </motion.div>
  )
}
