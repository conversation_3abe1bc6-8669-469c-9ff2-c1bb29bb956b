"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Download, ExternalLink } from "lucide-react"
import { Button } from "@/components/ui/button"

interface BrandBookResultProps {
  pdfUrls: Record<string, string>
  onReset: () => void
}

export function BrandBookResult({ pdfUrls, onReset }: BrandBookResultProps) {
  const [selectedPdf, setSelectedPdf] = useState<string | null>(
    Object.keys(pdfUrls).length > 0 ? Object.keys(pdfUrls)[0] : null
  )

  const handleDownload = (url: string, filename: string) => {
    const link = document.createElement("a")
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full p-6 bg-white rounded-xl shadow-lg"
    >
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-gray-800">Your Brand Book is Ready!</h3>
        <p className="text-gray-600 mt-1">
          We've also sent it to your email for safekeeping
        </p>
      </div>

      {Object.keys(pdfUrls).length > 1 && (
        <div className="flex flex-wrap gap-2 mb-4">
          {Object.entries(pdfUrls).map(([filename, _]) => (
            <Button
              key={filename}
              variant={selectedPdf === filename ? "default" : "outline"}
              onClick={() => setSelectedPdf(filename)}
              className="text-sm"
            >
              {filename.replace(/\.pdf$/i, "")}
            </Button>
          ))}
        </div>
      )}

      {selectedPdf && (
        <div className="aspect-[3/2] w-full bg-gray-100 rounded-lg overflow-hidden mb-4">
          <iframe
            src={`${pdfUrls[selectedPdf]}#toolbar=0&view=FitH`}
            className="w-full h-full border-0"
            title="Brand Book Preview"
          />
        </div>
      )}

      <div className="flex flex-col sm:flex-row gap-3 mt-6">
        {selectedPdf && (
          <>
            <Button
              onClick={() => handleDownload(pdfUrls[selectedPdf], selectedPdf)}
              className="flex items-center gap-2 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700"
            >
              <Download size={16} />
              <span>Download PDF</span>
            </Button>
            
            <Button
              variant="outline"
              onClick={() => window.open(pdfUrls[selectedPdf], "_blank")}
              className="flex items-center gap-2"
            >
              <ExternalLink size={16} />
              <span>Open in New Tab</span>
            </Button>
          </>
        )}
        
        <Button
          variant="ghost"
          onClick={onReset}
          className="sm:ml-auto"
        >
          Create Another Brand Book
        </Button>
      </div>
    </motion.div>
  )
}
