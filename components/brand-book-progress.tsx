"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Check, X, Loader2 } from "lucide-react"
import { SessionStatus } from "@/lib/api-client"

interface BrandBookProgressProps {
  sessionId: string
  status: SessionStatus | null
  email: string
  onComplete?: (pdfUrls: Record<string, string>) => void
}

export function BrandBookProgress({ 
  sessionId, 
  status, 
  email,
  onComplete 
}: BrandBookProgressProps) {
  const [progressPercentage, setProgressPercentage] = useState(0)

  useEffect(() => {
    if (!status) return
    
    // Calculate progress based on completed pages
    const totalPages = Object.keys(status.status.pages).length || 1
    const completedPages = Object.values(status.status.pages).filter(s => s === 'completed').length
    
    // Base progress on page generation (80%) and PDF creation (20%)
    let progress = (completedPages / totalPages) * 80
    
    // Add PDF progress
    if (Object.keys(status.status.pdfBundles).length > 0) {
      progress += 20
    }
    
    setProgressPercentage(Math.round(progress))
    
    // Call onComplete when PDF is ready
    if (progress === 100 && onComplete) {
      onComplete(status.status.pdfBundles)
    }
  }, [status, onComplete])

  const getStatusIcon = (statusValue: string) => {
    if (statusValue === 'completed') return <Check className="h-5 w-5 text-green-500" />
    if (statusValue === 'error') return <X className="h-5 w-5 text-red-500" />
    return <Loader2 className="h-5 w-5 text-indigo-500 animate-spin" />
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="w-full p-6 bg-white rounded-xl shadow-lg"
    >
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold text-gray-800">Creating Your Brand Book</h3>
        <p className="text-gray-600 mt-1">
          We'll send it to <span className="font-medium">{email}</span> when it's ready
        </p>
      </div>

      {/* Progress bar */}
      <div className="w-full h-4 bg-gray-200 rounded-full overflow-hidden mb-6">
        <motion.div
          className="h-full bg-gradient-to-r from-purple-600 to-indigo-600"
          initial={{ width: 0 }}
          animate={{ width: `${progressPercentage}%` }}
          transition={{ duration: 0.5 }}
        />
      </div>
      
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium text-gray-700">Progress</span>
        <span className="text-sm font-medium text-indigo-600">{progressPercentage}%</span>
      </div>

      {status && (
        <div className="space-y-3 mt-6">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Generating page configurations</span>
            {getStatusIcon(status.status.pageConfigs)}
          </div>
          
          {Object.entries(status.status.pages).map(([pageNum, pageStatus]) => (
            <div key={pageNum} className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Rendering page {pageNum}</span>
              {getStatusIcon(pageStatus)}
            </div>
          ))}
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Creating PDF</span>
            {getStatusIcon(Object.keys(status.status.pdfBundles).length > 0 ? 'completed' : 'pending')}
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Sending email</span>
            {getStatusIcon(status.status.email)}
          </div>
        </div>
      )}

      {status?.error && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">{status.error}</p>
        </div>
      )}
    </motion.div>
  )
}
