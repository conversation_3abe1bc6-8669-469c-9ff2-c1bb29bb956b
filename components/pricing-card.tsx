"use client"

import { motion } from "framer-motion"
import { Check } from "lucide-react"
import { Button } from "@/components/ui/button"

interface PricingCardProps {
  title: string
  price: string
  features: string[]
  icon: string
  highlighted?: boolean
  delay?: number
  onClick: () => void
}

export function PricingCard({
  title,
  price,
  features,
  icon,
  highlighted = false,
  delay = 0,
  onClick,
}: PricingCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.6, delay }}
      whileHover={{ y: -5 }}
      className={`rounded-2xl overflow-hidden ${
        highlighted ? "border-2 border-indigo-500 shadow-xl" : "border border-gray-200 shadow-lg"
      }`}
    >
      <div className={`p-8 ${highlighted ? "bg-gradient-to-r from-purple-600 to-indigo-600" : "bg-white"}`}>
        <div className="flex items-center justify-between">
          <h3 className={`text-2xl font-bold ${highlighted ? "text-white" : "text-gray-900"}`}>{title}</h3>
          <span className="text-3xl">{icon}</span>
        </div>
        <div className="mt-4">
          <span className={`text-4xl font-bold ${highlighted ? "text-white" : "text-gray-900"}`}>{price}</span>
        </div>
      </div>
      <div className="bg-white p-8">
        <ul className="space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start">
              <Check className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
              <span>{feature}</span>
            </li>
          ))}
        </ul>
        <div className="mt-8">
          <Button
            onClick={onClick}
            className={`w-full py-3 rounded-xl text-white font-medium ${
              highlighted
                ? "bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700"
                : "bg-gray-900 hover:bg-gray-800"
            }`}
          >
            Get Started
          </Button>
        </div>
      </div>
    </motion.div>
  )
}
