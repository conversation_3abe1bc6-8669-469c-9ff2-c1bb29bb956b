---
title: Guide · Cloudflare Workflows docs
description: Workflows allow you to build durable, multi-step applications using the Workers platform. A Workflow can automatically retry, persist state, run for hours or days, and coordinate between third-party APIs.
lastUpdated: 2025-04-06T20:34:04.000Z
source_url:
  html: https://developers.cloudflare.com/workflows/get-started/guide/
  md: https://developers.cloudflare.com/workflows/get-started/guide/index.md
---

Workflows allow you to build durable, multi-step applications using the Workers platform. A Workflow can automatically retry, persist state, run for hours or days, and coordinate between third-party APIs.

You can build Workflows to post-process file uploads to [R2 object storage](https://developers.cloudflare.com/r2/), automate generation of [Workers AI](https://developers.cloudflare.com/workers-ai/) embeddings into a [Vectorize](https://developers.cloudflare.com/vectorize/) vector database, or to trigger user lifecycle emails using your favorite email API.

This guide will instruct you through:

* Defining your first Workflow and publishing it
* Deploying the Workflow to your Cloudflare account
* Running (triggering) your Workflow and observing its output

At the end of this guide, you should be able to author, deploy and debug your own Workflows applications.

## Prerequisites

1. Sign up for a [Cloudflare account](https://dash.cloudflare.com/sign-up/workers-and-pages).
2. Install [`Node.js`](https://docs.npmjs.com/downloading-and-installing-node-js-and-npm).

Node.js version manager

Use a Node version manager like [Volta](https://volta.sh/) or [nvm](https://github.com/nvm-sh/nvm) to avoid permission issues and change Node.js versions. [Wrangler](https://developers.cloudflare.com/workers/wrangler/install-and-update/), discussed later in this guide, requires a Node version of `16.17.0` or later.

## 1. Define your Workflow

To create your first Workflow, use the `create cloudflare` (C3) CLI tool, specifying the Workflows starter template:

```sh
npm create cloudflare@latest workflows-starter -- --template "cloudflare/workflows-starter"
```

This will create a new folder called `workflows-starter`.

Open the `src/index.ts` file in your text editor. This file contains the following code, which is the most basic instance of a Workflow definition:

```ts
import { WorkflowEntrypoint, WorkflowStep, WorkflowEvent } from 'cloudflare:workers';


type Env = {
  // Add your bindings here, e.g. Workers KV, D1, Workers AI, etc.
  MY_WORKFLOW: Workflow;
};


// User-defined params passed to your workflow
type Params = {
  email: string;
  metadata: Record<string, string>;
};


export class MyWorkflow extends WorkflowEntrypoint<Env, Params> {
  async run(event: WorkflowEvent<Params>, step: WorkflowStep) {
    // Can access bindings on `this.env`
    // Can access params on `event.payload`


    const files = await step.do('my first step', async () => {
      // Fetch a list of files from $SOME_SERVICE
      return {
        files: [
          'doc_7392_rev3.pdf',
          'report_x29_final.pdf',
          'memo_2024_05_12.pdf',
          'file_089_update.pdf',
          'proj_alpha_v2.pdf',
          'data_analysis_q2.pdf',
          'notes_meeting_52.pdf',
          'summary_fy24_draft.pdf',
        ],
      };
    });


    const apiResponse = await step.do('some other step', async () => {
      let resp = await fetch('https://api.cloudflare.com/client/v4/ips');
      return await resp.json<any>();
    });


    await step.sleep('wait on something', '1 minute');


    await step.do(
      'make a call to write that could maybe, just might, fail',
      // Define a retry strategy
      {
        retries: {
          limit: 5,
          delay: '5 second',
          backoff: 'exponential',
        },
        timeout: '15 minutes',
      },
      async () => {
        // Do stuff here, with access to the state from our previous steps
        if (Math.random() > 0.5) {
          throw new Error('API call to $STORAGE_SYSTEM failed');
        }
      },
    );
  }
}
```

A Workflow definition:

1. Defines a `run` method that contains the primary logic for your workflow.
2. Has at least one or more calls to `step.do` that encapsulates the logic of your Workflow.
3. Allows steps to return (optional) state, allowing a Workflow to continue execution even if subsequent steps fail, without having to re-run all previous steps.

A single Worker application can contain multiple Workflow definitions, as long as each Workflow has a unique class name. This can be useful for code re-use or to define Workflows which are related to each other conceptually.

Each Workflow is otherwise entirely independent: a Worker that defines multiple Workflows is no different from a set of Workers that define one Workflow each.

## 2. Create your Workflows steps

Each `step` in a Workflow is an independently retriable function.

A `step` is what makes a Workflow powerful, as you can encapsulate errors and persist state as your Workflow progresses from step to step, avoiding your application from having to start from scratch on failure and ultimately build more reliable applications.

* A step can execute code (`step.do`) or sleep a Workflow (`step.sleep`).
* If a step fails (throws an exception), it will be automatically be retried based on your retry logic.
* If a step succeeds, any state it returns will be persisted within the Workflow.

At its most basic, a step looks like this:

```ts
// Import the Workflow definition
import { WorkflowEntrypoint, WorkflowEvent, WorkflowStep } from "cloudflare:workers"


type Params = {}


// Create your own class that implements a Workflow
export class MyWorkflow extends WorkflowEntrypoint<Env, Params> {
    // Define a run() method
    async run(event: WorkflowEvent<Params>, step: WorkflowStep) {
        // Define one or more steps that optionally return state.
        let state = step.do("my first step", async () => {


        })


        step.do("my second step", async () => {


        })
    }
}
```

Each call to `step.do` accepts three arguments:

1. (Required) A step name, which identifies the step in logs and telemetry
2. (Required) A callback function that contains the code to run for your step, and any state you want the Workflow to persist
3. (Optional) A `StepConfig` that defines the retry configuration (max retries, delay, and backoff algorithm) for the step

When trying to decide whether to break code up into more than one step, a good rule of thumb is to ask "do I want *all* of this code to run again if just one part of it fails?". In many cases, you do *not* want to repeatedly call an API if the following data processing stage fails, or if you get an error when attempting to send a completion or welcome email.

For example, each of the below tasks is ideally encapsulated in its own step, so that any failure — such as a file not existing, a third-party API being down or rate limited — does not cause your entire program to fail.

* Reading or writing files from [R2](https://developers.cloudflare.com/r2/)
* Running an AI task using [Workers AI](https://developers.cloudflare.com/workers-ai/)
* Querying a [D1 database](https://developers.cloudflare.com/d1/) or a database via [Hyperdrive](https://developers.cloudflare.com/hyperdrive/)
* Calling a third-party API

If a subsequent step fails, your Workflow can retry from that step, using any state returned from a previous step. This can also help you avoid unnecessarily querying a database or calling an paid API repeatedly for data you have already fetched.

Note

The term "Durable Execution" is widely used to describe this programming model.

"Durable" describes the ability of the program (application) to implicitly persist state without you having to manually write to an external store or serialize program state.

## 3. Configure your Workflow

Before you can deploy a Workflow, you need to configure it.

Open the Wrangler file at the root of your `workflows-starter` folder, which contains the following `[[workflows]]` configuration:

* wrangler.jsonc

  ```jsonc
  {
    "name": "workflows-starter",
    "main": "src/index.ts",
    "compatibility_date": "2024-10-22",
    "workflows": [
      {
        "name": "workflows-starter",
        "binding": "MY_WORKFLOW",
        "class_name": "MyWorkflow"
      }
    ]
  }
  ```

* wrangler.toml

  ```toml
  #:schema node_modules/wrangler/config-schema.json
  name = "workflows-starter"
  main = "src/index.ts"
  compatibility_date = "2024-10-22"


  [[workflows]]
  # name of your workflow
  name = "workflows-starter"
  # binding name env.MY_WORKFLOW
  binding = "MY_WORKFLOW"
  # this is class that extends the Workflow class in src/index.ts
  class_name = "MyWorkflow"
  ```

Note

If you have changed the name of the Workflow in your Wrangler commands, the JavaScript class name, or the name of the project you created, ensure that you update the values above to match the changes.

This configuration tells the Workers platform which JavaScript class represents your Workflow, and sets a `binding` name that allows you to run the Workflow from other handlers or to call into Workflows from other Workers scripts.

## 4. Bind to your Workflow

We have a very basic Workflow definition, but now need to provide a way to call it from within our code. A Workflow can be triggered by:

1. External HTTP requests via a `fetch()` handler
2. Messages from a [Queue](https://developers.cloudflare.com/queues/)
3. A schedule via [Cron Trigger](https://developers.cloudflare.com/workers/configuration/cron-triggers/)
4. Via the [Workflows REST API](https://developers.cloudflare.com/api/resources/workflows/methods/list/) or [wrangler CLI](https://developers.cloudflare.com/workers/wrangler/commands/#workflows)

Return to the `src/index.ts` file we created in the previous step and add a `fetch` handler that *binds* to our Workflow. This binding allows us to create new Workflow instances, fetch the status of an existing Workflow, pause and/or terminate a Workflow.

```ts
// This is in the same file as your Workflow definition


export default {
  async fetch(req: Request, env: Env): Promise<Response> {
    let url = new URL(req.url);


    if (url.pathname.startsWith('/favicon')) {
      return Response.json({}, { status: 404 });
    }


    // Get the status of an existing instance, if provided
    let id = url.searchParams.get('instanceId');
    if (id) {
      let instance = await env.MY_WORKFLOW.get(id);
      return Response.json({
        status: await instance.status(),
      });
    }


    // Spawn a new instance and return the ID and status
    let instance = await env.MY_WORKFLOW.create();
    return Response.json({
      id: instance.id,
      details: await instance.status(),
    });
  },
};
```

The code here exposes a HTTP endpoint that generates a random ID and runs the Workflow, returning the ID and the Workflow status. It also accepts an optional `instanceId` query parameter that retrieves the status of a Workflow instance by its ID.

Note

In a production application, you might choose to put authentication in front of your endpoint so that only authorized users can run a Workflow. Alternatively, you could pass messages to a Workflow [from a Queue consumer](https://developers.cloudflare.com/queues/reference/how-queues-works/#consumers) in order to allow for long-running tasks.

### Review your Workflow code

Note

This is the full contents of the `src/index.ts` file pulled down when you used the `cloudflare/workflows-starter` template at the beginning of this guide.

Before you deploy, you can review the full Workflows code and the `fetch` handler that will allow you to trigger your Workflow over HTTP:

```ts
import { WorkflowEntrypoint, WorkflowStep, WorkflowEvent } from 'cloudflare:workers';


type Env = {
  // Add your bindings here, e.g. Workers KV, D1, Workers AI, etc.
  MY_WORKFLOW: Workflow;
};


// User-defined params passed to your workflow
type Params = {
  email: string;
  metadata: Record<string, string>;
};


export class MyWorkflow extends WorkflowEntrypoint<Env, Params> {
  async run(event: WorkflowEvent<Params>, step: WorkflowStep) {
    // Can access bindings on `this.env`
    // Can access params on `event.payload`


    const files = await step.do('my first step', async () => {
      // Fetch a list of files from $SOME_SERVICE
      return {
        files: [
          'doc_7392_rev3.pdf',
          'report_x29_final.pdf',
          'memo_2024_05_12.pdf',
          'file_089_update.pdf',
          'proj_alpha_v2.pdf',
          'data_analysis_q2.pdf',
          'notes_meeting_52.pdf',
          'summary_fy24_draft.pdf',
        ],
      };
    });


    const apiResponse = await step.do('some other step', async () => {
      let resp = await fetch('https://api.cloudflare.com/client/v4/ips');
      return await resp.json<any>();
    });


    await step.sleep('wait on something', '1 minute');


    await step.do(
      'make a call to write that could maybe, just might, fail',
      // Define a retry strategy
      {
        retries: {
          limit: 5,
          delay: '5 second',
          backoff: 'exponential',
        },
        timeout: '15 minutes',
      },
      async () => {
        // Do stuff here, with access to the state from our previous steps
        if (Math.random() > 0.5) {
          throw new Error('API call to $STORAGE_SYSTEM failed');
        }
      },
    );
  }
}


export default {
  async fetch(req: Request, env: Env): Promise<Response> {
    let url = new URL(req.url);


    if (url.pathname.startsWith('/favicon')) {
      return Response.json({}, { status: 404 });
    }


    // Get the status of an existing instance, if provided
    let id = url.searchParams.get('instanceId');
    if (id) {
      let instance = await env.MY_WORKFLOW.get(id);
      return Response.json({
        status: await instance.status(),
      });
    }


    // Spawn a new instance and return the ID and status
    let instance = await env.MY_WORKFLOW.create();
    return Response.json({
      id: instance.id,
      details: await instance.status(),
    });
  },
};
```

## 5. Deploy your Workflow

Deploying a Workflow is identical to deploying a Worker.

```sh
npx wrangler deploy
```

```sh
# Note the "Workflows" binding mentioned here, showing that
# wrangler has detected your Workflow
Your worker has access to the following bindings:
- Workflows:
  - MY_WORKFLOW: MyWorkflow (defined in workflows-starter)
Uploaded workflows-starter (2.53 sec)
Deployed workflows-starter triggers (1.12 sec)
  https://workflows-starter.YOUR_WORKERS_SUBDOMAIN.workers.dev
  workflow: workflows-starter
```

A Worker with a valid Workflow definition will be automatically registered by Workflows. You can list your current Workflows using Wrangler:

```sh
npx wrangler workflows list
```

```sh
Showing last 1 workflow:
┌───────────────────┬───────────────────┬────────────┬─────────────────────────┬─────────────────────────┐
│ Name              │ Script name       │ Class name │ Created                 │ Modified                │
├───────────────────┼───────────────────┼────────────┼─────────────────────────┼─────────────────────────┤
│ workflows-starter │ workflows-starter │ MyWorkflow │ 10/23/2024, 11:33:58 AM │ 10/23/2024, 11:33:58 AM │
└───────────────────┴───────────────────┴────────────┴─────────────────────────┴─────────────────────────┘
```

## 6. Run and observe your Workflow

With your Workflow deployed, you can now run it.

1. A Workflow can run in parallel: each unique invocation of a Workflow is an *instance* of that Workflow.
2. An instance will run to completion (success or failure).
3. Deploying newer versions of a Workflow will cause all instances after that point to run the newest Workflow code.

Note

Because Workflows can be long running, it is possible to have running instances that represent different versions of your Workflow code over time.

To trigger our Workflow, we will use the `wrangler` CLI and pass in an optional `--payload`. The `payload` will be passed to your Workflow's `run` method handler as an `Event`.

```sh
npx wrangler workflows trigger workflows-starter '{"hello":"world"}'
```

```sh
# Workflow instance "12dc179f-9f77-4a37-b973-709dca4189ba" has been queued successfully
```

To inspect the current status of the Workflow instance we just triggered, we can either reference it by ID or by using the keyword `latest`:

```sh
npx wrangler@latest workflows instances describe workflows-starter latest
# Or by ID:
# npx wrangler@latest workflows instances describe workflows-starter 12dc179f-9f77-4a37-b973-709dca4189ba
```

```sh
Workflow Name:         workflows-starter
Instance Id:           f72c1648-dfa3-45ea-be66-b43d11d216f8
Version Id:            cedc33a0-11fa-4c26-8a8e-7d28d381a291
Status:                ✅ Completed
Trigger:               🌎 API
Queued:                10/15/2024, 1:55:31 PM
Success:               ✅ Yes
Start:                 10/15/2024, 1:55:31 PM
End:                   10/15/2024, 1:56:32 PM
Duration:              1 minute
Last Successful Step:  make a call to write that could maybe, just might, fail-1
Steps:


  Name:      my first step-1
  Type:      🎯 Step
  Start:     10/15/2024, 1:55:31 PM
  End:       10/15/2024, 1:55:31 PM
  Duration:  0 seconds
  Success:   ✅ Yes
  Output:    "{\"inputParams\":[{\"timestamp\":\"2024-10-15T13:55:29.363Z\",\"payload\":{\"hello\":\"world\"}}],\"files\":[\"doc_7392_rev3.pdf\",\"report_x29_final.pdf\",\"memo_2024_05_12.pdf\",\"file_089_update.pdf\",\"proj_alpha_v2.pdf\",\"data_analysis_q2.pdf\",\"notes_meeting_52.pdf\",\"summary_fy24_draft.pdf\",\"plan_2025_outline.pdf\"]}"
┌────────────────────────┬────────────────────────┬───────────┬────────────┐
│ Start                  │ End                    │ Duration  │ State      │
├────────────────────────┼────────────────────────┼───────────┼────────────┤
│ 10/15/2024, 1:55:31 PM │ 10/15/2024, 1:55:31 PM │ 0 seconds │ ✅ Success │
└────────────────────────┴────────────────────────┴───────────┴────────────┘


  Name:      some other step-1
  Type:      🎯 Step
  Start:     10/15/2024, 1:55:31 PM
  End:       10/15/2024, 1:55:31 PM
  Duration:  0 seconds
  Success:   ✅ Yes
  Output:    "{\"result\":{\"ipv4_cidrs\":[\"************/20\",\"************/22\",\"************/22\",\"**********/22\",\"************/18\",\"*************/18\",\"************/20\",\"************/20\",\"*************/22\",\"************/17\",\"***********/15\",\"**********/13\",\"**********/14\",\"**********/13\",\"**********/22\"],\"ipv6_cidrs\":[\"2400:cb00::/32\",\"2606:4700::/32\",\"2803:f800::/32\",\"2405:b500::/32\",\"2405:8100::/32\",\"2a06:98c0::/29\",\"2c0f:f248::/32\"],\"etag\":\"38f79d050aa027e3be3865e495dcc9bc\"},\"success\":true,\"errors\":[],\"messages\":[]}"
┌────────────────────────┬────────────────────────┬───────────┬────────────┐
│ Start                  │ End                    │ Duration  │ State      │
├────────────────────────┼────────────────────────┼───────────┼────────────┤
│ 10/15/2024, 1:55:31 PM │ 10/15/2024, 1:55:31 PM │ 0 seconds │ ✅ Success │
└────────────────────────┴────────────────────────┴───────────┴────────────┘


  Name:      wait on something-1
  Type:      💤 Sleeping
  Start:     10/15/2024, 1:55:31 PM
  End:       10/15/2024, 1:56:31 PM
  Duration:  1 minute


  Name:      make a call to write that could maybe, just might, fail-1
  Type:      🎯 Step
  Start:     10/15/2024, 1:56:31 PM
  End:       10/15/2024, 1:56:32 PM
  Duration:  1 second
  Success:   ✅ Yes
  Output:    null
┌────────────────────────┬────────────────────────┬───────────┬────────────┬───────────────────────────────────────────┐
│ Start                  │ End                    │ Duration  │ State      │ Error                                     │
├────────────────────────┼────────────────────────┼───────────┼────────────┼───────────────────────────────────────────┤
│ 10/15/2024, 1:56:31 PM │ 10/15/2024, 1:56:31 PM │ 0 seconds │ ❌ Error   │ Error: API call to $STORAGE_SYSTEM failed │
├────────────────────────┼────────────────────────┼───────────┼────────────┼───────────────────────────────────────────┤
│ 10/15/2024, 1:56:32 PM │ 10/15/2024, 1:56:32 PM │ 0 seconds │ ✅ Success │                                           │
└────────────────────────┴────────────────────────┴───────────┴────────────┴───────────────────────────────────────────┘
```

From the output above, we can inspect:

* The status (success, failure, running) of each step
* Any state emitted by the step
* Any `sleep` state, including when the Workflow will wake up
* Retries associated with each step
* Errors, including exception messages

Note

You do not have to wait for a Workflow instance to finish executing to inspect its current status. The `wrangler workflows instances describe` sub-command will show the status of an in-progress instance, including any persisted state, if it is sleeping, and any errors or retries. This can be especially useful when debugging a Workflow during development.

In the previous step, we also bound a Workers script to our Workflow. You can trigger a Workflow by visiting the (deployed) Workers script in a browser or with any HTTP client.

```sh
# This must match the URL provided in step 6
curl -s https://workflows-starter.YOUR_WORKERS_SUBDOMAIN.workers.dev/
```

```sh
{"id":"16ac31e5-db9d-48ae-a58f-95b95422d0fa","details":{"status":"queued","error":null,"output":null}}
```

***

## Next steps

* Learn more about [how events are passed to a Workflow](https://developers.cloudflare.com/workflows/build/events-and-parameters/).
* Learn more about binding to and triggering Workflow instances using the [Workers API](https://developers.cloudflare.com/workflows/build/workers-api/).
* Learn more about the [Rules of Workflows](https://developers.cloudflare.com/workflows/build/rules-of-workflows/) and best practices for building applications using Workflows.

If you have any feature requests or notice any bugs, share your feedback directly with the Cloudflare team by joining the [Cloudflare Developers community on Discord](https://discord.cloudflare.com).


---
title: Rules of Workflows · Cloudflare Workflows docs
description: A Workflow contains one or more steps. Each step is a self-contained, individually retriable component of a Workflow. Steps may emit (optional) state that allows a Workflow to persist and continue from that step, even if a Workflow fails due to a network or infrastructure issue.
lastUpdated: 2025-04-18T15:48:47.000Z
source_url:
  html: https://developers.cloudflare.com/workflows/build/rules-of-workflows/
  md: https://developers.cloudflare.com/workflows/build/rules-of-workflows/index.md
---

A Workflow contains one or more steps. Each step is a self-contained, individually retriable component of a Workflow. Steps may emit (optional) state that allows a Workflow to persist and continue from that step, even if a Workflow fails due to a network or infrastructure issue.

This is a small guidebook on how to build more resilient and correct Workflows.

### Ensure API/Binding calls are idempotent

Because a step might be retried multiple times, your steps should (ideally) be idempotent. For context, idempotency is a logical property where the operation (in this case a step), can be applied multiple times without changing the result beyond the initial application.

As an example, let us assume you have a Workflow that charges your customers, and you really do not want to charge them twice by accident. Before charging them, you should check if they were already charged:

* JavaScript

  ```js
  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event, step) {
      const customer_id = 123456;
      // ✅ Good: Non-idempotent API/Binding calls are always done **after** checking if the operation is
      // still needed.
      await step.do(
        `charge ${customer_id} for its monthly subscription`,
        async () => {
          // API call to check if customer was already charged
          const subscription = await fetch(
            `https://payment.processor/subscriptions/${customer_id}`,
          ).then((res) => res.json());


          // return early if the customer was already charged, this can happen if the destination service dies
          // in the middle of the request but still commits it, or if the Workflows Engine restarts.
          if (subscription.charged) {
            return;
          }


          // non-idempotent call, this operation can fail and retry but still commit in the payment
          // processor - which means that, on retry, it would mischarge the customer again if the above checks
          // were not in place.
          return await fetch(
            `https://payment.processor/subscriptions/${customer_id}`,
            {
              method: "POST",
              body: JSON.stringify({ amount: 10.0 }),
            },
          );
        },
      );
    }
  }
  ```

* TypeScript

  ```ts
  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event: WorkflowEvent<Params>, step: WorkflowStep) {
      const customer_id = 123456;
      // ✅ Good: Non-idempotent API/Binding calls are always done **after** checking if the operation is
      // still needed.
      await step.do(
        `charge ${customer_id} for its monthly subscription`,
        async () => {
          // API call to check if customer was already charged
          const subscription = await fetch(
            `https://payment.processor/subscriptions/${customer_id}`,
          ).then((res) => res.json());


          // return early if the customer was already charged, this can happen if the destination service dies
          // in the middle of the request but still commits it, or if the Workflows Engine restarts.
          if (subscription.charged) {
            return;
          }


          // non-idempotent call, this operation can fail and retry but still commit in the payment
          // processor - which means that, on retry, it would mischarge the customer again if the above checks
          // were not in place.
          return await fetch(
            `https://payment.processor/subscriptions/${customer_id}`,
            {
              method: "POST",
              body: JSON.stringify({ amount: 10.0 }),
            },
          );
        },
      );
    }
  }
  ```

Note

Guaranteeing idempotency might be optional in your specific use-case and implementation, but we recommend that you always try to guarantee it.

### Make your steps granular

Steps should be as self-contained as possible. This allows your own logic to be more durable in case of failures in third-party APIs, network errors, and so on.

You can also think of it as a transaction, or a unit of work.

* ✅ Minimize the number of API/binding calls per step (unless you need multiple calls to prove idempotency).

- JavaScript

  ```js
  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event, step) {
      // ✅ Good: Unrelated API/Binding calls are self-contained, so that in case one of them fails
      // it can retry them individually. It also has an extra advantage: you can control retry or
      // timeout policies for each granular step - you might not to want to overload http.cat in
      // case of it being down.
      const httpCat = await step.do("get cutest cat from KV", async () => {
        return await env.KV.get("cutest-http-cat");
      });


      const image = await step.do("fetch cat image from http.cat", async () => {
        return await fetch(`https://http.cat/${httpCat}`);
      });
    }
  }
  ```

- TypeScript

  ```ts
  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event: WorkflowEvent<Params>, step: WorkflowStep) {
      // ✅ Good: Unrelated API/Binding calls are self-contained, so that in case one of them fails
      // it can retry them individually. It also has an extra advantage: you can control retry or
      // timeout policies for each granular step - you might not to want to overload http.cat in
      // case of it being down.
      const httpCat = await step.do("get cutest cat from KV", async () => {
        return await env.KV.get("cutest-http-cat");
      });


      const image = await step.do("fetch cat image from http.cat", async () => {
        return await fetch(`https://http.cat/${httpCat}`);
      });
    }
  }
  ```

Otherwise, your entire Workflow might not be as durable as you might think, and you may encounter some undefined behaviour. You can avoid them by following the rules below:

* 🔴 Do not encapsulate your entire logic in one single step.
* 🔴 Do not call separate services in the same step (unless you need it to prove idempotency).
* 🔴 Do not make too many service calls in the same step (unless you need it to prove idempotency).
* 🔴 Do not do too much CPU-intensive work inside a single step - sometimes the engine may have to restart, and it will start over from the beginning of that step.

- JavaScript

  ```js
  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event, step) {
      // 🔴 Bad: you are calling two separate services from within the same step. This might cause
      // some extra calls to the first service in case the second one fails, and in some cases, makes
      // the step non-idempotent altogether
      const image = await step.do("get cutest cat from KV", async () => {
        const httpCat = await env.KV.get("cutest-http-cat");
        return fetch(`https://http.cat/${httpCat}`);
      });
    }
  }
  ```

- TypeScript

  ```ts
  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event: WorkflowEvent<Params>, step: WorkflowStep) {
      // 🔴 Bad: you are calling two separate services from within the same step. This might cause
      // some extra calls to the first service in case the second one fails, and in some cases, makes
      // the step non-idempotent altogether
      const image = await step.do("get cutest cat from KV", async () => {
        const httpCat = await env.KV.get("cutest-http-cat");
        return fetch(`https://http.cat/${httpCat}`);
      });
    }
  }
  ```

### Do not rely on state outside of a step

Workflows may hibernate and lose all in-memory state. This will happen when engine detects that there is no pending work and can hibernate until it needs to wake-up (because of a sleep, retry, or event).

This means that you should not store state outside of a step:

* JavaScript

  ```js
  function getRandomInt(min, max) {
    const minCeiled = Math.ceil(min);
    const maxFloored = Math.floor(max);
    return Math.floor(Math.random() * (maxFloored - minCeiled) + minCeiled); // The maximum is exclusive and the minimum is inclusive
  }


  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event, step) {
      // 🔴 Bad: `imageList` will be not persisted across engine's lifetimes. Which means that after hibernation,
      // `imageList` will be empty again, even though the following two steps have already ran.
      const imageList = [];


      await step.do("get first cutest cat from KV", async () => {
        const httpCat = await env.KV.get("cutest-http-cat-1");


        imageList.append(httpCat);
      });


      await step.do("get second cutest cat from KV", async () => {
        const httpCat = await env.KV.get("cutest-http-cat-2");


        imageList.append(httpCat);
      });


      // A long sleep can (and probably will) hibernate the engine which means that the first engine lifetime ends here
      await step.sleep("💤💤💤💤", "3 hours");


      // When this runs, it will be on the second engine lifetime - which means `imageList` will be empty.
      await step.do(
        "choose a random cat from the list and download it",
        async () => {
          const randomCat = imageList.at(getRandomInt(0, imageList.length));
          // this will fail since `randomCat` is undefined because `imageList` is empty
          return await fetch(`https://http.cat/${randomCat}`);
        },
      );
    }
  }
  ```

* TypeScript

  ```ts
  function getRandomInt(min, max) {
    const minCeiled = Math.ceil(min);
    const maxFloored = Math.floor(max);
    return Math.floor(Math.random() * (maxFloored - minCeiled) + minCeiled); // The maximum is exclusive and the minimum is inclusive
  }


  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event: WorkflowEvent<Params>, step: WorkflowStep) {
      // 🔴 Bad: `imageList` will be not persisted across engine's lifetimes. Which means that after hibernation,
      // `imageList` will be empty again, even though the following two steps have already ran.
      const imageList: string[] = [];


      await step.do("get first cutest cat from KV", async () => {
        const httpCat = await env.KV.get("cutest-http-cat-1");


        imageList.append(httpCat);
      });


      await step.do("get second cutest cat from KV", async () => {
        const httpCat = await env.KV.get("cutest-http-cat-2");


        imageList.append(httpCat);
      });


      // A long sleep can (and probably will) hibernate the engine which means that the first engine lifetime ends here
      await step.sleep("💤💤💤💤", "3 hours");


      // When this runs, it will be on the second engine lifetime - which means `imageList` will be empty.
      await step.do(
        "choose a random cat from the list and download it",
        async () => {
          const randomCat = imageList.at(getRandomInt(0, imageList.length));
          // this will fail since `randomCat` is undefined because `imageList` is empty
          return await fetch(`https://http.cat/${randomCat}`);
        },
      );
    }
  }
  ```

Instead, you should build top-level state exclusively comprised of `step.do` returns:

* JavaScript

  ```js
  function getRandomInt(min, max) {
    const minCeiled = Math.ceil(min);
    const maxFloored = Math.floor(max);
    return Math.floor(Math.random() * (maxFloored - minCeiled) + minCeiled); // The maximum is exclusive and the minimum is inclusive
  }


  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event, step) {
      // ✅ Good: imageList state is exclusively comprised of step returns - this means that in the event of
      // multiple engine lifetimes, imageList will be built accordingly
      const imageList = await Promise.all([
        step.do("get first cutest cat from KV", async () => {
          return await env.KV.get("cutest-http-cat-1");
        }),


        step.do("get second cutest cat from KV", async () => {
          return await env.KV.get("cutest-http-cat-2");
        }),
      ]);


      // A long sleep can (and probably will) hibernate the engine which means that the first engine lifetime ends here
      await step.sleep("💤💤💤💤", "3 hours");


      // When this runs, it will be on the second engine lifetime - but this time, imageList will contain
      // the two most cutest cats
      await step.do(
        "choose a random cat from the list and download it",
        async () => {
          const randomCat = imageList.at(getRandomInt(0, imageList.length));
          // this will eventually succeed since `randomCat` is defined
          return await fetch(`https://http.cat/${randomCat}`);
        },
      );
    }
  }
  ```

* TypeScript

  ```ts
  function getRandomInt(min, max) {
    const minCeiled = Math.ceil(min);
    const maxFloored = Math.floor(max);
    return Math.floor(Math.random() * (maxFloored - minCeiled) + minCeiled); // The maximum is exclusive and the minimum is inclusive
  }


  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event: WorkflowEvent<Params>, step: WorkflowStep) {
      // ✅ Good: imageList state is exclusively comprised of step returns - this means that in the event of
      // multiple engine lifetimes, imageList will be built accordingly
      const imageList: string[] = await Promise.all([
        step.do("get first cutest cat from KV", async () => {
          return await env.KV.get("cutest-http-cat-1");
        }),


        step.do("get second cutest cat from KV", async () => {
          return await env.KV.get("cutest-http-cat-2");
        }),
      ]);


      // A long sleep can (and probably will) hibernate the engine which means that the first engine lifetime ends here
      await step.sleep("💤💤💤💤", "3 hours");


      // When this runs, it will be on the second engine lifetime - but this time, imageList will contain
      // the two most cutest cats
      await step.do(
        "choose a random cat from the list and download it",
        async () => {
          const randomCat = imageList.at(getRandomInt(0, imageList.length));
          // this will eventually succeed since `randomCat` is defined
          return await fetch(`https://http.cat/${randomCat}`);
        },
      );
    }
  }
  ```

### Do not mutate your incoming events

The `event` passed to your Workflow's `run` method is immutable: changes you make to the event are not persisted across steps and/or Workflow restarts.

* JavaScript

  ```js
  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event, step) {
      // 🔴 Bad: Mutating the event
      // This will not be persisted across steps and `event.payload` will
      // take on its original value.
      await step.do("bad step that mutates the incoming event", async () => {
        let userData = await env.KV.get(event.payload.user);
        event.payload = userData;
      });


      // ✅ Good: persist data by returning it as state from your step
      // Use that state in subsequent steps
      let userData = await step.do("good step that returns state", async () => {
        return await env.KV.get(event.payload.user);
      });


      let someOtherData = await step.do(
        "following step that uses that state",
        async () => {
          // Access to userData here
          // Will always be the same if this step is retried
        },
      );
    }
  }
  ```

* TypeScript

  ```ts
  interface MyEvent {
    user: string;
    data: string;
  }


  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event: WorkflowEvent<MyEvent>, step: WorkflowStep) {
      // 🔴 Bad: Mutating the event
      // This will not be persisted across steps and `event.payload` will
      // take on its original value.
      await step.do("bad step that mutates the incoming event", async () => {
          let userData = await env.KV.get(event.payload.user)
          event.payload = userData
      })


      // ✅ Good: persist data by returning it as state from your step
      // Use that state in subsequent steps
      let userData = await step.do("good step that returns state", async () => {
        return await env.KV.get(event.payload.user)
      })


      let someOtherData = await step.do("following step that uses that state", async () => {
        // Access to userData here
        // Will always be the same if this step is retried
      })
    }
  }
  ```

### Name steps deterministically

Steps should be named deterministically (that is, not using the current date/time, randomness, etc). This ensures that their state is cached, and prevents the step from being rerun unnecessarily. Step names act as the "cache key" in your Workflow.

* JavaScript

  ```js
  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event, step) {
      // 🔴 Bad: Naming the step non-deterministically prevents it from being cached
      // This will cause the step to be re-run if subsequent steps fail.
      await step.do(`step #1 running at: ${Date.now()}`, async () => {
        let userData = await env.KV.get(event.payload.user);
        // Do not mutate event.payload
        event.payload = userData;
      });


      // ✅ Good: give steps a deterministic name.
      // Return dynamic values in your state, or log them instead.
      let state = await step.do("fetch user data from KV", async () => {
        let userData = await env.KV.get(event.payload.user);
        console.log(`fetched at ${Date.now}`);
        return userData;
      });


      // ✅ Good: steps that are dynamically named are constructed in a deterministic way.
      // In this case, `catList` is a step output, which is stable, and `catList` is
      // traversed in a deterministic fashion (no shuffles or random accesses) so,
      // it's fine to dynamically name steps (e.g: create a step per list entry).
      let catList = await step.do("get cat list from KV", async () => {
        return await env.KV.get("cat-list");
      });


      for (const cat of catList) {
        await step.do(`get cat: ${cat}`, async () => {
          return await env.KV.get(cat);
        });
      }
    }
  }
  ```

* TypeScript

  ```ts
  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event: WorkflowEvent<Params>, step: WorkflowStep) {
      // 🔴 Bad: Naming the step non-deterministically prevents it from being cached
      // This will cause the step to be re-run if subsequent steps fail.
      await step.do(`step #1 running at: ${Date.now()}`, async () => {
          let userData = await env.KV.get(event.payload.user)
          // Do not mutate event.payload
          event.payload = userData
      })


      // ✅ Good: give steps a deterministic name.
      // Return dynamic values in your state, or log them instead.
      let state = await step.do("fetch user data from KV", async () => {
          let userData = await env.KV.get(event.payload.user)
          console.log(`fetched at ${Date.now}`)
          return userData
      })


      // ✅ Good: steps that are dynamically named are constructed in a deterministic way.
      // In this case, `catList` is a step output, which is stable, and `catList` is
      // traversed in a deterministic fashion (no shuffles or random accesses) so,
      // it's fine to dynamically name steps (e.g: create a step per list entry).
      let catList = await step.do("get cat list from KV", async () => {
        return await env.KV.get("cat-list")
      })


      for(const cat of catList) {
        await step.do(`get cat: ${cat}`, async () => {
          return await env.KV.get(cat)
        })
      }
    }
  }
  ```

### Take care with `Promise.race()` and `Promise.any()`

Workflows allows the usage steps within the `Promise.race()` or `Promise.any()` methods as a way to achieve concurrent steps execution. However, some considerations must be taken.

Due to the nature of Workflows' instance lifecycle, and given that a step inside a Promise will run until it finishes, the step that is returned during the first passage may not be the actual cached step, as [steps are cached by their names](#name-steps-deterministically).

* JavaScript

  ```js
  // helper sleep method
  const sleep = (ms) => new Promise((r) => setTimeout(r, ms));


  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event, step) {
      // 🔴 Bad: The `Promise.race` is not surrounded by a `step.do`, which may cause undeterministic caching behavior.
      const race_return = await Promise.race([
        step.do("Promise first race", async () => {
          await sleep(1000);
          return "first";
        }),
        step.do("Promise second race", async () => {
          return "second";
        }),
      ]);


      await step.sleep("Sleep step", "2 hours");


      return await step.do("Another step", async () => {
        // This step will return `first`, even though the `Promise.race` first returned `second`.
        return race_return;
      });
    }
  }
  ```

* TypeScript

  ```ts
  // helper sleep method
  const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));


  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event: WorkflowEvent<Params>, step: WorkflowStep) {
      // 🔴 Bad: The `Promise.race` is not surrounded by a `step.do`, which may cause undeterministic caching behavior.
      const race_return = await Promise.race(
        [
          step.do(
            'Promise first race',
            async () => {
              await sleep(1000);
              return "first";
            }
          ),
          step.do(
            'Promise second race',
            async () => {
              return "second";
            }
          ),
        ]
      );


      await step.sleep("Sleep step", "2 hours");


      return await step.do(
        'Another step',
        async () => {
          // This step will return `first`, even though the `Promise.race` first returned `second`.
          return race_return;
        },
      );
    }
  }
  ```

To ensure consistency, we suggest to surround the `Promise.race()` or `Promise.any()` within a `step.do()`, as this will ensure caching consistency across multiple passages.

* JavaScript

  ```js
  // helper sleep method
  const sleep = (ms) => new Promise((r) => setTimeout(r, ms));


  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event, step) {
      // ✅ Good: The `Promise.race` is surrounded by a `step.do`, ensuring deterministic caching behavior.
      const race_return = await step.do("Promise step", async () => {
        return await Promise.race([
          step.do("Promise first race", async () => {
            await sleep(1000);
            return "first";
          }),
          step.do("Promise second race", async () => {
            return "second";
          }),
        ]);
      });


      await step.sleep("Sleep step", "2 hours");


      return await step.do("Another step", async () => {
        // This step will return `second` because the `Promise.race` was surround by the `step.do` method.
        return race_return;
      });
    }
  }
  ```

* TypeScript

  ```ts
  // helper sleep method
  const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));


  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event: WorkflowEvent<Params>, step: WorkflowStep) {
      // ✅ Good: The `Promise.race` is surrounded by a `step.do`, ensuring deterministic caching behavior.
      const race_return = await step.do(
        'Promise step',
        async () => {
          return await Promise.race(
            [
              step.do(
                'Promise first race',
                async () => {
                  await sleep(1000);
                  return "first";
                }
              ),
              step.do(
                'Promise second race',
                async () => {
                  return "second";
                }
              ),
            ]
          );
        }
      );


      await step.sleep("Sleep step", "2 hours");


      return await step.do(
        'Another step',
        async () => {
          // This step will return `second` because the `Promise.race` was surround by the `step.do` method.
          return race_return;
        },
      );
    }
  }
  ```

### Instance IDs are unique

Workflow [instance IDs](https://developers.cloudflare.com/workflows/build/workers-api/#workflowinstance) are unique per Workflow. The ID is the unique identifier that associates logs, metrics, state and status of a run to a specific instance, even after completion. Allowing ID re-use would make it hard to understand if a Workflow instance ID referred to an instance that run yesterday, last week or today.

It would also present a problem if you wanted to run multiple different Workflow instances with different [input parameters](https://developers.cloudflare.com/workflows/build/events-and-parameters/) for the same user ID, as you would immediately need to determine a new ID mapping.

If you need to associate multiple instances with a specific user, merchant or other "customer" ID in your system, consider using a composite ID or using randomly generated IDs and storing the mapping in a database like [D1](https://developers.cloudflare.com/d1/).

* JavaScript

  ```js
  // This is in the same file as your Workflow definition
  export default {
    async fetch(req, env) {
      // 🔴 Bad: Use an ID that isn't unique across future Workflow invocations
      let userId = getUserId(req); // Returns the userId
      let badInstance = await env.MY_WORKFLOW.create({
        id: userId,
        params: payload,
      });


      // ✅ Good: use an ID that is unique
      // e.g. a transaction ID, order ID, or task ID are good options
      let instanceId = getTransactionId(); // e.g. assuming transaction IDs are unique
      // or: compose a composite ID and store it in your database
      // so that you can track all instances associated with a specific user or merchant.
      instanceId = `${getUserId(request)}-${await crypto.randomUUID().slice(0, 6)}`;
      let { result } = await addNewInstanceToDB(userId, instanceId);
      let goodInstance = await env.MY_WORKFLOW.create({
        id: userId,
        params: payload,
      });


      return Response.json({
        id: goodInstance.id,
        details: await goodInstance.status(),
      });
    },
  };
  ```

* TypeScript

  ```ts
  // This is in the same file as your Workflow definition
  export default {
    async fetch(req: Request, env: Env): Promise<Response> {
      // 🔴 Bad: Use an ID that isn't unique across future Workflow invocations
      let userId = getUserId(req) // Returns the userId
      let badInstance = await env.MY_WORKFLOW.create({
        id: userId,
        params: payload
      });


      // ✅ Good: use an ID that is unique
      // e.g. a transaction ID, order ID, or task ID are good options
      let instanceId = getTransactionId() // e.g. assuming transaction IDs are unique
      // or: compose a composite ID and store it in your database
      // so that you can track all instances associated with a specific user or merchant.
      instanceId = `${getUserId(request)}-${await crypto.randomUUID().slice(0, 6)}`
      let { result } = await addNewInstanceToDB(userId, instanceId)
      let goodInstance = await env.MY_WORKFLOW.create({
        id: userId,
        params: payload
      });


      return Response.json({
        id: goodInstance.id,
        details: await goodInstance.status(),
      });
    },
  };
  ```

### `await` your steps

When calling `step.do` or `step.sleep`, use `await` to avoid introducing bugs and race conditions into your Workflow code.

If you don't call `await step.do` or `await step.sleep`, you create a dangling Promise. This occurs when a Promise is created but not properly `await`ed, leading to potential bugs and race conditions.

This happens when you do not use the `await` keyword or fail to chain `.then()` methods to handle the result of a Promise. For example, calling `fetch(GITHUB_URL)` without awaiting its response will cause subsequent code to execute immediately, regardless of whether the fetch completed. This can cause issues like premature logging, exceptions being swallowed (and not terminating the Workflow), and lost return values (state).

* JavaScript

  ```js
  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event, step) {
      // 🔴 Bad: The step isn't await'ed, and any state or errors is swallowed before it returns.
      const issues = step.do(`fetch issues from GitHub`, async () => {
        // The step will return before this call is done
        let issues = await getIssues(event.payload.repoName);
        return issues;
      });


      // ✅ Good: The step is correctly await'ed.
      const issues = await step.do(`fetch issues from GitHub`, async () => {
        let issues = await getIssues(event.payload.repoName);
        return issues;
      });


      // Rest of your Workflow goes here!
    }
  }
  ```

* TypeScript

  ```ts
  export class MyWorkflow extends WorkflowEntrypoint {
    async run(event: WorkflowEvent<Params>, step: WorkflowStep) {
      // 🔴 Bad: The step isn't await'ed, and any state or errors is swallowed before it returns.
      const issues = step.do(`fetch issues from GitHub`, async () => {
          // The step will return before this call is done
          let issues = await getIssues(event.payload.repoName)
          return issues
      })


      // ✅ Good: The step is correctly await'ed.
      const issues = await step.do(`fetch issues from GitHub`, async () => {
          let issues = await getIssues(event.payload.repoName)
          return issues
      })


      // Rest of your Workflow goes here!
    }
  }
  ```

### Batch multiple Workflow invocations

When creating multiple Workflow instances, use the [`createBatch`](https://developers.cloudflare.com/workflows/build/workers-api/#createBatch) method to batch the invocations together. This allows you to create multiple Workflow instances in a single request, which will reduce the number of requests made to the Workflows API and increase the number of instances you can create per minute.

* JavaScript

  ```js
  export default {
    async fetch(req, env) {
      let instances = [
        { id: "user1", params: { name: "John" } },
        { id: "user2", params: { name: "Jane" } },
        { id: "user3", params: { name: "Alice" } },
        { id: "user4", params: { name: "Bob" } },
      ];


      // 🔴 Bad: Create them one by one, which is more likely to hit creation rate limits.
      for (let instance of instances) {
        await env.MY_WORKFLOW.create({
          id: instance.id,
          params: instance.params,
        });
      }


      // ✅ Good: Batch calls together
      // This improves throughput.
      let instances = await env.MY_WORKFLOW.createBatch(instances);
      return Response.json({ instances });
    },
  };
  ```

* TypeScript

  ```ts
  export default {
    async fetch(req: Request, env: Env): Promise<Response> {
      let instances = [{"id": "user1", "params": {"name": "John"}}, {"id": "user2", "params": {"name": "Jane"}}, {"id": "user3", "params": {"name": "Alice"}}, {"id": "user4", "params": {"name": "Bob"}}];


      // 🔴 Bad: Create them one by one, which is more likely to hit creation rate limits.
      for (let instance of instances) {
        await env.MY_WORKFLOW.create({
          id: instance.id,
          params: instance.params
        });
      }


      // ✅ Good: Batch calls together
      // This improves throughput.
      let instances = await env.MY_WORKFLOW.createBatch(instances);
      return Response.json({ instances })
    },
  };
  ```
---
title: Trigger Workflows · Cloudflare Workflows docs
description: You can trigger Workflows both programmatically and via the Workflows APIs, including:
lastUpdated: 2025-02-18T11:28:45.000Z
source_url:
  html: https://developers.cloudflare.com/workflows/build/trigger-workflows/
  md: https://developers.cloudflare.com/workflows/build/trigger-workflows/index.md
---

You can trigger Workflows both programmatically and via the Workflows APIs, including:

1. With [Workers](https://developers.cloudflare.com/workers) via HTTP requests in a `fetch` handler, or bindings from a `queue` or `scheduled` handler
2. Using the [Workflows REST API](https://developers.cloudflare.com/api/resources/workflows/methods/list/)
3. Via the [wrangler CLI](https://developers.cloudflare.com/workers/wrangler/commands/#workflows) in your terminal

## Workers API (Bindings)

You can interact with Workflows programmatically from any Worker script by creating a binding to a Workflow. A Worker can bind to multiple Workflows, including Workflows defined in other Workers projects (scripts) within your account.

You can interact with a Workflow:

* Directly over HTTP via the [`fetch`](https://developers.cloudflare.com/workers/runtime-apis/handlers/fetch/) handler
* From a [Queue consumer](https://developers.cloudflare.com/queues/configuration/javascript-apis/#consumer) inside a `queue` handler
* From a [Cron Trigger](https://developers.cloudflare.com/workers/configuration/cron-triggers/) inside a `scheduled` handler
* Within a [Durable Object](https://developers.cloudflare.com/durable-objects/)

Note

New to Workflows? Start with the [Workflows tutorial](https://developers.cloudflare.com/workflows/get-started/guide/) to deploy your first Workflow and familiarize yourself with Workflows concepts.

To bind to a Workflow from your Workers code, you need to define a [binding](https://developers.cloudflare.com/workers/wrangler/configuration/) to a specific Workflow. For example, to bind to the Workflow defined in the [get started guide](https://developers.cloudflare.com/workflows/get-started/guide/), you would configure the [Wrangler configuration file](https://developers.cloudflare.com/workers/wrangler/configuration/) with the below:

* wrangler.jsonc

  ```jsonc
  {
    "name": "workflows-tutorial",
    "main": "src/index.ts",
    "compatibility_date": "2024-10-22",
    "workflows": [
      {
        "name": "workflows-tutorial",
        "binding": "MY_WORKFLOW",
        "class_name": "MyWorkflow"
      }
    ]
  }
  ```

* wrangler.toml

  ```toml
  name = "workflows-tutorial"
  main = "src/index.ts"
  compatibility_date = "2024-10-22"


  [[workflows]]
  # The name of the Workflow
  name = "workflows-tutorial"
  # The binding name, which must be a valid JavaScript variable name.  This will
  # be how you call (run) your Workflow from your other Workers handlers or
  # scripts.
  binding = "MY_WORKFLOW"
  # Must match the class defined in your code that extends the Workflow class
  class_name = "MyWorkflow"
  ```

The `binding = "MY_WORKFLOW"` line defines the JavaScript variable that our Workflow methods are accessible on, including `create` (which triggers a new instance) or `get` (which returns the status of an existing instance).

The following example shows how you can manage Workflows from within a Worker, including:

* Retrieving the status of an existing Workflow instance by its ID
* Creating (triggering) a new Workflow instance
* Returning the status of a given instance ID

```ts
interface Env {
  MY_WORKFLOW: Workflow;
}


export default {
  async fetch(req: Request, env: Env) {
    // Get instanceId from query parameters
    const instanceId = new URL(req.url).searchParams.get("instanceId")


    // If an ?instanceId=<id> query parameter is provided, fetch the status
    // of an existing Workflow by its ID.
    if (instanceId) {
      let instance = await env.MY_WORKFLOW.get(instanceId);
      return Response.json({
        status: await instance.status(),
      });
    }


    // Else, create a new instance of our Workflow, passing in any (optional)
    // params and return the ID.
    const newId = await crypto.randomUUID();
    let instance = await env.MY_WORKFLOW.create({ id: newId });
    return Response.json({
      id: instance.id,
      details: await instance.status(),
    });


    return Response.json({ result });
  },
};
```

### Inspect a Workflow's status

You can inspect the status of any running Workflow instance by calling `status` against a specific instance ID. This allows you to programmatically inspect whether an instance is queued (waiting to be scheduled), actively running, paused, or errored.

```ts
let instance = await env.MY_WORKFLOW.get("abc-123")
let status = await instance.status() // Returns an InstanceStatus
```

The possible values of status are as follows:

```ts
  status:
    | "queued" // means that instance is waiting to be started (see concurrency limits)
    | "running"
    | "paused"
    | "errored"
    | "terminated" // user terminated the instance while it was running
    | "complete"
    | "waiting" // instance is hibernating and waiting for sleep or event to finish
    | "waitingForPause" // instance is finishing the current work to pause
    | "unknown";
  error?: string;
  output?: object;
};
```

### Stop a Workflow

You can stop/terminate a Workflow instance by calling `terminate` against a specific instance ID.

```ts
let instance = await env.MY_WORKFLOW.get("abc-123")
await instance.terminate() // Returns Promise<void>
```

Once stopped/terminated, the Workflow instance *cannot* be resumed.

### Restart a Workflow

Warning

**Known issue**: Restarting a Workflow via the `restart()` method is not currently supported and will throw an exception (error).

```ts
let instance = await env.MY_WORKFLOW.get("abc-123")
await instance.restart() // Returns Promise<void>
```

Restarting an instance will immediately cancel any in-progress steps, erase any intermediate state, and treat the Workflow as if it was run for the first time.

## REST API (HTTP)

Refer to the [Workflows REST API documentation](https://developers.cloudflare.com/api/resources/workflows/subresources/instances/methods/create/).

## Command line (CLI)

Refer to the [CLI quick start](https://developers.cloudflare.com/workflows/get-started/cli-quick-start/) to learn more about how to manage and trigger Workflows via the command-line.
---
title: Sleeping and retrying · Cloudflare Workflows docs
description: This guide details how to sleep a Workflow and/or configure retries for a Workflow step.
lastUpdated: 2025-02-18T11:28:45.000Z
source_url:
  html: https://developers.cloudflare.com/workflows/build/sleeping-and-retrying/
  md: https://developers.cloudflare.com/workflows/build/sleeping-and-retrying/index.md
---

This guide details how to sleep a Workflow and/or configure retries for a Workflow step.

## Sleep a Workflow

You can set a Workflow to sleep as an explicit step, which can be useful when you want a Workflow to wait, schedule work ahead, or pause until an input or other external state is ready.

Note

A Workflow instance that is resuming from sleep will take priority over newly scheduled (queued) instances. This helps ensure that older Workflow instances can run to completion and are not blocked by newer instances.

### Sleep for a relative period

Use `step.sleep` to have a Workflow sleep for a relative period of time:

```ts
await step.sleep("sleep for a bit", "1 hour")
```

The second argument to `step.sleep` accepts both `number` (milliseconds) or a human-readable format, such as "1 minute" or "26 hours". The accepted units for `step.sleep` when used this way are as follows:

```ts
| "second"
| "minute"
| "hour"
| "day"
| "week"
| "month"
| "year"
```

### Sleep until a fixed date

Use `step.sleepUntil` to have a Workflow sleep to a specific `Date`: this can be useful when you have a timestamp from another system or want to "schedule" work to occur at a specific time (e.g. Sunday, 9AM UTC).

```ts
// sleepUntil accepts a Date object as its second argument
const workflowsLaunchDate = Date.parse("24 Oct 2024 13:00:00 UTC");
await step.sleepUntil("sleep until X times out", workflowsLaunchDate)
```

You can also provide a UNIX timestamp (milliseconds since the UNIX epoch) directly to `sleepUntil`.

## Retry steps

Each call to `step.do` in a Workflow accepts an optional `StepConfig`, which allows you define the retry behaviour for that step.

If you do not provide your own retry configuration, Workflows applies the following defaults:

```ts
const defaultConfig: WorkflowStepConfig = {
  retries: {
    limit: 5,
    delay: 10000,
    backoff: 'exponential',
  },
  timeout: '10 minutes',
};
```

When providing your own `StepConfig`, you can configure:

* The total number of attempts to make for a step (accepts `Infinity` for unlimited retries)
* The delay between attempts (accepts both `number` (ms) or a human-readable format)
* What backoff algorithm to apply between each attempt: any of `constant`, `linear`, or `exponential`
* When to timeout (in duration) before considering the step as failed (including during a retry attempt)

For example, to limit a step to 10 retries and have it apply an exponential delay (starting at 10 seconds) between each attempt, you would pass the following configuration as an optional object to `step.do`:

```ts
let someState = step.do("call an API", {
  retries: {
    limit: 10, // The total number of attempts
    delay: "10 seconds", // Delay between each retry
    backoff: "exponential" // Any of "constant" | "linear" | "exponential";
  },
  timeout: "30 minutes",
}, async () => { /* Step code goes here /* }
```

## Force a Workflow instance to fail

You can also force a Workflow instance to fail and *not* retry by throwing a `NonRetryableError` from within the step.

This can be useful when you detect a terminal (permanent) error from an upstream system (such as an authentication failure) or other errors where retrying would not help.

```ts
// Import the NonRetryableError definition
import { WorkflowEntrypoint, WorkflowStep, WorkflowEvent } from 'cloudflare:workers';
import { NonRetryableError } from 'cloudflare:workflows';


// In your step code:
export class MyWorkflow extends WorkflowEntrypoint<Env, Params> {
  async run(event: WorkflowEvent<Params>, step: WorkflowStep) {
    await step.do("some step", async () => {
        if !(event.payload.data) {
          throw new NonRetryableError("event.payload.data did not contain the expected payload")
        }
      })
  }
}
```

The Workflow instance itself will fail immediately, no further steps will be invoked, and the Workflow will not be retried.

## Catch Workflow errors

Any uncaught exceptions that propagate to the top level, or any steps that reach their retry limit, will cause the Workflow to end execution in an `Errored` state.

If you want to avoid this, you can catch exceptions emitted by a `step`. This can be useful if you need to trigger clean-up tasks or have conditional logic that triggers additional steps.

To allow the Workflow to continue its execution, surround the intended steps that are allowed to fail with a `try-catch` block.

```ts
...
await step.do('task', async () => {
  // work to be done
});


try {
    await step.do('non-retryable-task', async () => {
    // work not to be retried
        throw new NonRetryableError('oh no');
    });
} catch(e as Error) {
    console.log(`Step failed: ${e.message}`);
    await step.do('clean-up-task', async () => {
      // Clean up code here
    });
}


// the Workflow will not fail and will continue its execution


await step.do('next-task', async() => {
  // more work to be done
});
...
```
