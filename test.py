import requests
import json
import base64
import os
import hashlib
import hmac
import time
import argparse
import sys

# --- API Endpoint URLs ---
BASE_URL = 'https://mybrandbook-backend.thoughtseedlabs.workers.dev'
SUBMIT_FORM_URL = f'{BASE_URL}/submit-form'
WEBHOOK_URL = f'{BASE_URL}/webhook'
GENERATE_CONFIG_URL = f'{BASE_URL}/generate-config'
GENERATE_BRANDBOOK_URL = f'{BASE_URL}/generate-brandbook'
SESSION_URL = f'{BASE_URL}/session/'

# --- Webhook Secret ---
WEBHOOK_SECRET = 'whsec_LDscPW97qCpySxt4NnLE6E4b'

# --- Payloads and Headers ---

# 1. Submit Form Data
submit_form_payload = {
    "brandName": "Acme Corp",
    "industry": "Technology",
    "description": "A leading provider of tech solutions.",
    "targetAudience": "Startups and SMBs",
    "vibes": ["innovative", "trustworthy", "modern"],
    "email": "<EMAIL>",
    "plan": "basic"
}
submit_form_headers = {
    'Content-Type': 'application/json'
}

# 2. Generate Config Data
generate_config_headers = {
    'Content-Type': 'application/json'
}

# 3. Generate Brandbook Headers
generate_brandbook_headers = {
    'Content-Type': 'application/json'
}

def make_request(url, method='POST', headers=None, data=None):
    print(f"\n--- Making {method} request to: {url} ---")
    try:
        if method.upper() == 'POST':
            # If 'data' is already a string and Content-Type suggests it's JSON, send as raw data.
            # Otherwise, if 'data' is a dict, let requests handle JSON serialization via the 'json' param.
            if isinstance(data, str) and headers and headers.get('Content-Type') == 'application/json':
                response = requests.post(url, headers=headers, data=data, timeout=180)
            else:
                response = requests.post(url, headers=headers, json=data, timeout=180) # For dicts or other auto-handling
        elif method.upper() == 'GET':
            response = requests.get(url, headers=headers, timeout=30)
        else:
            print(f"Unsupported HTTP method: {method}")
            return None

        print(f"Status Code: {response.status_code}")
        try:
            response_json = response.json()
            print("Response JSON:")
            print(json.dumps(response_json, indent=2))
            return response_json
        except json.JSONDecodeError:
            print("Response Content (not JSON):")
            print(response.text)
            return response.text

    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return None

def generate_webhook_signature(payload_dict, timestamp, webhook_id):
    # Ensure payload is stringified exactly as it will be sent and signed (compact form)
    payload_string_compact = json.dumps(payload_dict, separators=(',', ':'))

    # Construct the message string with exact format: webhook_id.timestamp.payload_string_compact
    message = f"{webhook_id}.{timestamp}.{payload_string_compact}"

    # Generate HMAC-SHA256 signature
    signature = hmac.new(
        WEBHOOK_SECRET.encode('utf-8'),
        message.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()

    return signature, payload_string_compact # Return both signature and the string used

def save_base64_image(base64_string, output_filename="output_image.png"):
    try:
        image_data = base64.b64decode(base64_string)
        with open(output_filename, 'wb') as f:
            f.write(image_data)
        print(f"\nSuccessfully saved image to {os.path.abspath(output_filename)}")
    except base64.binascii.Error as e:
        print(f"Failed to decode base64 string: {e}")
    except Exception as e:
        print(f"Failed to save image: {e}")

def extract_base64_images(response, prefix="brandbook_page"):
    if not response:
        print("No response data to extract images from")
        return

    if isinstance(response, list):
        for i, page_data in enumerate(response):
            if isinstance(page_data, dict) and 'data' in page_data and 'b64_json' in page_data['data']:
                output_filename = f"{prefix}_{page_data.get('pageNumber', i+1)}.png"
                save_base64_image(page_data['data']['b64_json'], output_filename)
            elif isinstance(page_data, dict):
                # Try to find base64 data in any field
                for key, value in page_data.items():
                    if isinstance(value, dict) and 'b64_json' in value:
                        output_filename = f"{prefix}_{page_data.get('pageNumber', i+1)}.png"
                        save_base64_image(value['b64_json'], output_filename)
                        break
    elif isinstance(response, dict):
        # Handle case where response is a single object
        if 'data' in response and 'b64_json' in response['data']:
            save_base64_image(response['data']['b64_json'], f"{prefix}.png")

def print_debug_info(debug_data):
    """Print debug information in a structured format"""
    if not debug_data:
        print("No debug information available")
        return

    print("\n===== DEBUG INFORMATION =====\n")

    # Print API parameters if available
    for key, value in debug_data.items():
        if "_page_" in key:
            page_num = key.split("_page_")[1].split("_")[0]
            print(f"\n--- Page {page_num} ---")

            if "error" in key:
                print("ERROR DETAILS:")
                if isinstance(value, dict) and "error" in value:
                    error = value.get("error", {})
                    print(f"Message: {error.get('message', 'Unknown')}")
                    print(f"Status: {error.get('status', 'Unknown')}")
                    print(f"Code: {error.get('code', 'Unknown')}")
                    print(f"Type: {error.get('type', 'Unknown')}")
                    print(f"Parameter: {error.get('param', 'Unknown')}")
                else:
                    print(value)
            elif "response" in key:
                print("RESPONSE DETAILS:")
                print(json.dumps(value, indent=2))
            else:
                # Print API parameters
                if isinstance(value, dict) and "apiParams" in value:
                    print("API PARAMETERS:")
                    api_params = value.get("apiParams", {})
                    print(json.dumps(api_params, indent=2))
                    print(f"API Key Present: {value.get('apiKey', 'Unknown')}")

def print_job_receipts(session_data):
    """Print job receipt information"""
    job_receipts = {}

    # Extract job receipts from session data
    for key, value in session_data.items():
        if key.startswith("job_receipt_"):
            job_receipts[key] = value

    if not job_receipts:
        return

    print("\n===== JOB RECEIPTS =====\n")
    for job_id, receipt in job_receipts.items():
        print(f"Job ID: {job_id}")
        print(f"Timestamp: {receipt.get('timestamp')}")
        print(f"Job Type: {receipt.get('jobType')}")
        print(f"Session ID: {receipt.get('sessionId')}")
        print("\n")

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Test the Brandbook API')
    parser.add_argument('--verbose', '-v', action='store_true', help='Show verbose debug output')
    parser.add_argument('--session', '-s', type=str, help='Use existing session ID instead of creating new one')
    args = parser.parse_args()

    # Set verbosity
    verbose = args.verbose

    print("Starting API test sequence...")

    # Step 1: Submit Form
    print("\nStep 1: Submitting form...")
    submit_form_response = make_request(SUBMIT_FORM_URL, headers=submit_form_headers, data=submit_form_payload)

    # Extract sessionId from the response or use provided one
    session_id = None
    if args.session:
        session_id = args.session
        print(f"Using provided sessionId: {session_id}")
    elif submit_form_response and isinstance(submit_form_response, dict) and 'sessionId' in submit_form_response:
        session_id = submit_form_response['sessionId']
        print(f"Extracted sessionId: {session_id}")
    else:
        print("Could not extract sessionId from submit_form_response or response was not as expected.")
        # Use a default session ID for testing if needed
        session_id = "sess_2c29d2755ab0"
        print(f"Using default sessionId for testing: {session_id}")

    # Step 2: Webhook (simulate payment success)
    print("\nStep 2: Sending webhook...")

    # Create webhook payload as a dictionary first
    webhook_payload_dict = {
        "type": "payment.succeeded",
        "data": {
            "metadata": {
                "sessionId": session_id
            },
            "amount": 1000,
            "currency": "USD"
        }
    }

    # Generate webhook headers with proper signature
    webhook_id = 'teststringId'
    webhook_timestamp = str(int(time.time()))
    # Get both signature and the exact payload string used for signing
    webhook_signature, payload_string_for_request = generate_webhook_signature(webhook_payload_dict, webhook_timestamp, webhook_id)

    webhook_headers = {
        'Content-Type': 'application/json', # Important: requests needs this to know data is json-formatted string
        'webhook-id': webhook_id,
        'webhook-timestamp': webhook_timestamp,
        'webhook-signature': webhook_signature
    }

    print("\n=== Webhook Request Details ===")
    print("Headers:")
    print(json.dumps(webhook_headers, indent=2))
    print("Payload string being sent and signed:") # For debugging
    print(payload_string_for_request)             # For debugging

    # Send the exact payload_string_for_request as the body
    # The make_request function will handle it if 'data' is a string and Content-Type is application/json
    webhook_response = make_request(WEBHOOK_URL, headers=webhook_headers, data=payload_string_for_request)

    # Wait a moment for server processing
    print("\nWaiting for webhook processing...")
    time.sleep(5)  # Increased wait time for processing

    # Step 3: Wait and check session status (the webhook will trigger config generation internally)
    print("\nStep 3: Waiting for config generation...")

    # Wait for processing (increase this time if needed)
    time.sleep(10)  # Increased wait time for processing

    # Check session status to see if configs were generated
    session_status_url = f"{SESSION_URL}{session_id}"
    session_response = make_request(session_status_url, method="GET")

    # Extract config information from session status
    brandbook_configs = None
    if session_response and isinstance(session_response, dict) and session_response.get('status'):
        status = session_response.get('status')
        # Check if configs were generated successfully
        if status.get('pageConfigs') != "error":
            print("Config generation appears successful")
        else:
            print(f"Config generation error: {session_response.get('error', 'Unknown error')}")

    # Since configs are handled internally, use session_id for brandbook generation
    # instead of trying to extract configs from response
    else:
        print("Using default configs from API documentation")
        # Use the sample configs from the API docs as backup
        brandbook_configs = [
            {
                "pageNumber": 1,
                "prompt": "Create a professional brand book page titled \"Logo Guidelines\"...",  # This is truncated for brevity
                "imageParams": {
                    "model": "gpt-image-1",
                    "size": "1536x1024",
                    "quality": "high",
                    "style": "photorealistic",
                    "output_format": "png", # Changed from response_format as it's not supported for gpt-image-1
                    "background": "transparent"
                }
            },
            # Add more sample configs if needed
        ]

    # Step 4: Check brandbook status or request generation if needed
    print("\nStep 4: Checking brandbook status...")

    # Our updated API only needs the sessionId
    brandbook_payload = {
        "sessionId": session_id
    }

    # Use the same endpoint for status checks and triggering generation
    brandbook_response = make_request(GENERATE_BRANDBOOK_URL, headers=generate_brandbook_headers, data=brandbook_payload)

    # Print the status response
    if brandbook_response and 'success' in brandbook_response:
        if brandbook_response['success']:
            print(f"Brandbook status: Success - {brandbook_response.get('message', 'No message')}")
            # If there are jobIds, the process has started
            if 'jobIds' in brandbook_response and brandbook_response['jobIds']:
                print(f"Job IDs: {len(brandbook_response['jobIds'])} jobs queued or in progress")
        else:
            print(f"Brandbook status: Failed - {brandbook_response.get('message', 'No message')}")
            if 'error' in brandbook_response:
                print(f"Error: {brandbook_response['error']}")

    # Step 5: Poll Session Status (wait for processing to complete)
    print("\nStep 5: Polling session status for up to 60 seconds...")

    session_status_url = f"{SESSION_URL}{session_id}"
    max_polls = 15  # Increased number of polls
    poll_interval = 8  # Increased wait time between polls

    for poll_count in range(max_polls):
        print(f"\nPoll attempt {poll_count + 1}/{max_polls}...")

        session_status_response = make_request(session_status_url, method="GET")

        if not session_status_response:
            print("Failed to retrieve session status")
            break

        status = session_status_response.get('status', {})
        page_configs_status = status.get('pageConfigs', 'unknown')
        job_ids = session_status_response.get('jobIds', [])
        debug_data = session_status_response.get('debug', {})
        error = session_status_response.get('error')

        if "jobIds" in session_status_response and len(session_status_response["jobIds"]) > 0:
            print(f"Current status: pageConfigs={page_configs_status}, jobIds={len(session_status_response['jobIds'])}")
        else:
            print(f"Current status: pageConfigs={page_configs_status}")

        # Print any errors
        if error:
            print(f"\nERROR: {error}")

        # Print debug information if verbose mode is enabled
        if verbose and debug_data:
            print_debug_info(debug_data)
            print_job_receipts(debug_data)

        # Print page configs if available
        if "pageConfigs" in session_status_response:
            print("\nPage Configs:")
            print(json.dumps(session_status_response["pageConfigs"], indent=2))

        # Print prompts if available
        if "prompts" in session_status_response:
            print("\nPrompts:")
            print(json.dumps(session_status_response["prompts"], indent=2))

        # Check if processing has completed or failed
        if page_configs_status == "completed":
            print("Config generation complete!")
            print(f"Job IDs: {job_ids}")

            # Check if any image jobs have started
            if len(job_ids) > 1:  # More than the initial config job
                print(f"Image generation jobs queued: {len(job_ids)-1}")

            break
        elif page_configs_status == "error":
            print(f"Config generation failed: {session_status_response.get('error', 'Unknown error')}")
            break

        # If still pending, wait and try again
        if poll_count < max_polls - 1:
            print(f"Still pending... waiting {poll_interval} seconds")

            # Check for debug data even while pending
            if verbose and 'debug' in session_status_response:
                print_debug_info(session_status_response.get('debug', {}))

            # Check for errors even while pending
            if 'error' in session_status_response and session_status_response['error']:
                print(f"\nERROR DETECTED: {session_status_response['error']}")
            time.sleep(poll_interval)

    print("\nFinal session status:")
    print(json.dumps(session_status_response, indent=2))

    print("\nTest sequence completed!")