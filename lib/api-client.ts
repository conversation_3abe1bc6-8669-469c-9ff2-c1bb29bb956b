/**
 * API client for interacting with the backend
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'https://api.mybrandbook.ai';

/**
 * Form data interface
 */
export interface BrandBookFormData {
  brandName: string;
  industry: string;
  description: string;
  targetAudience: string;
  vibes: string[];
  email: string;
  plan: "basic" | "pro";
}

/**
 * Session response interface
 */
export interface SessionResponse {
  sessionId: string;
}

/**
 * Session status interface
 */
export interface SessionStatus {
  formData: BrandBookFormData;
  timestamp: number;
  status: {
    pageConfigs: "pending" | "completed" | "error";
    pages: Record<number, "pending" | "completed" | "error">;
    pdfBundles: Record<string, string>; // filename -> URL
    email: "pending" | "sent" | "error";
  };
  error?: string;
}

/**
 * Submit form data to create a brand book generation session
 * @param formData Brand book form data
 * @returns Session response with sessionId
 */
export async function submitBrandBookForm(formData: BrandBookFormData): Promise<SessionResponse> {
  const response = await fetch(`${API_BASE_URL}/submit-form`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(formData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to submit form');
  }

  return response.json();
}

/**
 * Get the status of a brand book generation session
 * @param sessionId Session ID
 * @returns Session status
 */
export async function getSessionStatus(sessionId: string): Promise<SessionStatus> {
  const response = await fetch(`${API_BASE_URL}/session/${sessionId}`);

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to get session status');
  }

  return response.json();
}

/**
 * Poll the session status until it's completed or an error occurs
 * @param sessionId Session ID
 * @param onStatusUpdate Callback for status updates
 * @returns Final session status
 */
export async function pollSessionStatus(
  sessionId: string,
  onStatusUpdate: (status: SessionStatus) => void,
  intervalMs = 3000,
  maxAttempts = 60
): Promise<SessionStatus> {
  let attempts = 0;

  return new Promise((resolve, reject) => {
    const poll = async () => {
      if (attempts >= maxAttempts) {
        reject(new Error('Polling timed out'));
        return;
      }

      try {
        const status = await getSessionStatus(sessionId);
        onStatusUpdate(status);

        // Check if all pages are completed and PDF is ready
        const allPagesCompleted = Object.values(status.status.pages).every(
          (pageStatus) => pageStatus === 'completed'
        );
        
        const pdfReady = Object.keys(status.status.pdfBundles).length > 0;
        
        if (allPagesCompleted && pdfReady) {
          resolve(status);
          return;
        }

        // Check for errors
        if (status.status.pageConfigs === 'error' || status.error) {
          reject(new Error(status.error || 'An error occurred during brand book generation'));
          return;
        }

        // Continue polling
        attempts++;
        setTimeout(poll, intervalMs);
      } catch (error) {
        reject(error);
      }
    };

    // Start polling
    poll();
  });
}
